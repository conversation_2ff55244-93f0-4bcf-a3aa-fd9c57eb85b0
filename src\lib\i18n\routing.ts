import { defineRouting } from 'next-intl/routing'
import { locales, defaultLocale } from './locales'

/**
 * Valid locale prefix strategies
 */
type LocalePrefix = 'always' | 'as-needed' | 'never';

/**
 * Get locale prefix strategy from environment variable
 */
const getLocalePrefix = (): LocalePrefix => {
  const localePrefix = process.env.NEXT_PUBLIC_LOCALE_PREFIX as LocalePrefix;
  const validPrefixes: LocalePrefix[] = ['always', 'as-needed', 'never'];

  return validPrefixes.includes(localePrefix) ? localePrefix : 'never';
};

/**
 * Routing configuration for the app.
 */
export const routing = defineRouting({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Configurable locale prefix strategy via environment variable
  localePrefix: getLocalePrefix()
});

