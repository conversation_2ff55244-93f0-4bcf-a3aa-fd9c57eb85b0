import { defineRouting } from 'next-intl/routing'
import { locales, defaultLocale } from './locales'

/**
 * Get locale prefix strategy from environment variable
 * Options: 'always', 'as-needed', 'never'
 */
const getLocalePrefix = () => {
  const envPrefix = process.env.NEXT_PUBLIC_LOCALE_PREFIX;

  // Validate against allowed values
  if (envPrefix === 'always' || envPrefix === 'as-needed' || envPrefix === 'never') {
    return envPrefix;
  }

  // Default fallback
  return 'never';
};

/**
 * Routing configuration for the app.
 */
export const routing = defineRouting({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Configurable locale prefix strategy via environment variable
  localePrefix: getLocalePrefix()
});

