'use client';

import { useContext } from 'react';
import dynamic from 'next/dynamic';
import TimeBasedTitle from '@/components/ClientComponents/TimeBasedTitle';
import OrderSection from '@/containers/checkout/OrderSection';
import GuaranteeSection from '@/containers/checkout/GuaranteeSection';
import BillingFaqSection from '@/containers/checkout/BillingFaqSection';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import PaymentGateway from '@/components/PaymentGateway';

const DynamicTimeBasedTitle = dynamic(() => import('@/components/ClientComponents/TimeBasedTitle'), {
  ssr: false,
  loading: () => <TimeBasedTitle />,
});

const CheckoutV1 = () => {
  const { paymentSystem } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const { getIsTrial } = useContext(SessionContext);
  const isTrial = getIsTrial();

  return (
    <div className="md:px-[5.69444%] max-w-[1920px] m-auto mt-10">
      <div>
        <DynamicTimeBasedTitle />
      </div>
      <div className="flex flex-wrap justify-between">
        <div className="flex flex-col flex-wrap justify-center lg:w-[calc(50%-20px)] mb-[30px] md:mb-0">
          <OrderSection compliantVersion={time < 330} />
          <GuaranteeSection />
        </div>
        <div className="flex flex-wrap lg:w-[calc(50%-20px)]">
          {paymentSystem ? <PaymentGateway paymentSystem={paymentSystem} isTrial={isTrial} /> : null}
        </div>
      </div>
      <div>
        <BillingFaqSection />
      </div>
    </div>
  );
};
export default CheckoutV1;