'use client';
import { FC, useContext } from 'react';
import { menuItems } from '@/app/config';
import MenuItem from '@/components/Menu/MenuItem';
import { usePathname } from '@/lib/i18n/navigation';
import useLayoutStore from '@/store/useLayoutStore';
import { UserContext } from '@/store/UserContext';

interface MobileMenuProps {}

const MobileMenu: FC<MobileMenuProps> = ({}) => {
  const { user } = useContext(UserContext);
  const { mobileOpen, handleMobileOpen } = useLayoutStore();
  const activePath = usePathname();
  
  return (
    <div
      className={`transition-transform duration-500 ease-in-out ${mobileOpen ? "translate-x-0" : "translate-x-full"} w-screen h-screen bg-white z-10 fixed top-0 left-0 flex flex-col justify-center items-center`}
    >
      <div
        className='flex flex-col px-2'
      >
        <nav className={`flex flex-col flex-wrap gap-10 text-center lg:flex mobile-menu`}>
          {menuItems.filter(x => !(activePath == '/results' && x.id == 'pricing') &&  !user ? !x.memberOnly : !x.hideIfLoggedIn).map((item, _i) => (
            <MenuItem key={item.id} {...{ item }} />
          ))}
        </nav>
      </div>
    </div>
  );
};

export default MobileMenu;
