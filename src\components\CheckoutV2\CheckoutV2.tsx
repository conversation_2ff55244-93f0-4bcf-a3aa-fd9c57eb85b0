'use client';

import { useState, useEffect, useRef, useContext, useMemo, Suspense } from 'react';
import Cookies from 'js-cookie';
import Image from 'next/image';
import { Link } from '@/lib/i18n/navigation';
import Head from 'next/head';
import { useRouter } from '@/lib/i18n/navigation';
import IqCard from '@/components/IqCard';
import AccordionCheckout from '@/components/AccordionCheckout';
import PauseOnHover from '@/components/CarouselAutoplay';
import ImproveCard from '@/components/ImproveCard';
import LatestResultPart from '@/components/LatestResultPart';
import Navbartwo from '@/components/Navbartwo';
import ReviewsCarouselCheckout from '@/components/ReviewsCheckout';
import countries from '@/containers/home/<USER>';
import SessionContext from '@/store/SessionContext';
import JustBoughtFunction from '@/components/Navbartwo/just_bought';
import PaymentGateway from '@/components/PaymentGateway';

export default function CheckoutV2() {
  const router = useRouter();
  const { getIsTrial, locale, updateLocale, formData, paymentSystem } = useContext(SessionContext);
  const { prices } = useContext(SessionContext);
  const scroll = useRef<HTMLDivElement | null>(null);
  const scroll2 = useRef<HTMLDivElement | null>(null);
  const [namesOnTable, setNamesOnTable] = useState<string[]>([]);
  const [peopleFromCountry, setPeopleFromCountry] = useState([ { 'First name': '', 'Last name': '', 'IQ score': '', Country: '' } ]);
  const [peopleArray, setArray] = useState([ { name: '', image: '', IQ: 102, } ]);
  const isTrial = getIsTrial();

  const scrollToElement = () => {
    if (scroll.current) {
      scroll.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToElementMobile = () => {
    if (scroll2.current) {
      scroll2.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const country = useMemo(() => {
    const locale = Cookies.get('locale')?.toLowerCase();
    return (
      countries.find((c) => c.id === locale) || {
        id: 'us',
        iq: 97.43,
        name: 'United States',
      }
    );
  }, []);

  useEffect(() => {
    updateLocale(Cookies.get('locale') || '');
  }, [locale, updateLocale]);

  useEffect(() => {
    setPeopleFromCountry(JustBoughtFunction(country?.name));
  }, [country]);

  useEffect(() => {
    setNamesOnTable(peopleArray.slice(0, 8).map(person => person.name));
  }, [peopleArray]);

  const namesRef = useRef(new Set()); // Ref to track the names on the table without causing re-renders

  useEffect(() => {
    if (peopleFromCountry.length > 0) {
      updateContent(({ name, image, number }: { name: string; image: string; number: number }) => {
        if (name) {
          // Add name to the namesRef set without causing re-renders
          namesRef.current.add(name);
          const newResult = { name, image, IQ: number };
          setArray(prevArray => [newResult, ...prevArray]);
        } else {
          console.log('No valid name returned');
        }
      }, peopleArray);

      const interval = setInterval(() => {
        updateContent(({ name, image, number }: { name: string; image: string; number: number }) => {
          if (name) {
            // Check if the name is already in the set, if not add it
            if (!namesRef.current.has(name)) {
              namesRef.current.add(name);
              const newResult = { name, image, IQ: number };
              setArray(prevArray => [newResult, ...prevArray]);
            }
          } else {
            console.log('No valid name returned');
          }
        }, peopleArray);
      }, 15000);

      return () => clearInterval(interval);
    }
  }, [peopleFromCountry]);

  function updateContent(callback: any, currentArray: any) {
    const getRandomContent = () => {
      if (peopleFromCountry.length === 0) {
        console.log('No more people available');
        return;
      }

      var existingNames = new Set(namesOnTable);

      const availablePeople = peopleFromCountry.filter(person => {
        const fullName = `${person['First name']} ${person['Last name']}`.trim();
        return !existingNames.has(fullName) && fullName;
      });

      if (availablePeople.length === 0) {
        console.log('No unique people left to select');
        return;
      }

      const randomIndex = Math.floor(Math.random() * availablePeople.length);
      const randomPerson = availablePeople[randomIndex];
      const randomPeopleCountryImage = countries.find(c => c.name === randomPerson['Country']);

      const name = `${randomPerson['First name']} ${randomPerson['Last name']}`.trim();

      callback({
        name,
        image: `https://flagcdn.com/w80/${randomPeopleCountryImage?.id}.png`,
        number: randomPerson['IQ score'],
      });
    };

    getRandomContent();
  }

  return (
    <div className="overflow-hidden grow flex justify-center">
      <Head>
        <link rel="stylesheet" href="https://assets.reviews.io/css/widgets/carousel-widget.css?_t=2024100119" />
        <link rel="stylesheet" href="https://assets.reviews.io/iconfont/reviewsio-icons/style.css?_t=2024100119" />
      </Head>
      <div className="w-full md:w-[2500px] mx-auto overflow-hidden">
        <div className="w-full md:max-w-[1260px] mx-auto">
          <div className="mx-auto px-2">
            <div className="relative flex items-center justify-between  h-[82px]">
              <div className="flex flex-1 items-center justify-between sm:items-stretch sm:justify-start">
                <div className="flex flex-shrink-0 items-center">
                  <Link href="/">
                    <Image
                      src="/images/Logo.svg"
                      alt="Academy Logo"
                      className="h-8 w-auto hidden md:block"
                      width={300}
                      height={36}
                      priority // Load it early because it's part of header/logo
                    />
                  </Link>
                  <Link href="/">
                    <Image width={41} height={30} src="/images/logo-mobile.svg" className="block md:hidden" alt={''} />
                  </Link>
                </div>
                <div className=" sm:ml-auto sm:block">
                  <div className="flex justify-center content-center space-x-2">
                    <div className="text-white rounded-full flex justify-center content-center font-[20px] bg-[#FF932F] w-[32px] h-[32px] text-center">
                      <p className="pt-1 text-[#191919]">{formData.email[0]?.toUpperCase()}</p>
                    </div>
                    <p className="pt-1 text-[#191919] font-bold">{formData.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {peopleArray[0].name != '' && (
          <Navbartwo name={peopleArray[0].name} image={peopleArray[0].image} IQ={peopleArray[0].IQ} />
        )}
        <div className="text-center py-3 bg-[#FF932F]">
          <p className="text-[white] font-light text-[16px]">
            Special Welcome Offer! Get{' '}
            <b>{Math.round((1 - prices.oneTime.amount / prices.subscription.amount) * 100)}%</b> Discount Today!
          </p>
        </div>
        <div className="bg-gradient-to-r from-[#FFFFFF] to-[#F6F9FF]">
          <div className="bg-gradient-to-r from-[#FFFFFF] md:flex justify-between flex-row-reverse to-[#F6F9FF] px-3 md:px-0 py-6 md:py-16 max-w-[1276px] m-auto">
            <div className="flex w-full justify-center md:w-[343px] md:w-auto m-auto">
              <IqCard imgSource="elon" name="Elon Musk" text="IQ 164" />
              <div className="md:pt-20 md:ml-4 mt-3 md:mt-0">
                <IqCard imgSource="me" name="You" text="IQ ???" />
              </div>
              <div className="md:pt-10 md:ml-4 mt-6 md:mt-0">
                <IqCard imgSource="gomez" name="Selena Gomez" text="IQ 112" />
              </div>
            </div>
            <div>
              <h1 className="text-[#191919] text-[38px] leading-[38px] md:text-[68px] py-6 pl-7 md:py-8 md:pr-[60px] font-bold tracking-[3%] md:leading-[78px]">
                Your IQ score <br /> <span className="text-[#01cb84]">is ready!</span>
              </h1>
              <ul>
                <li className="flex text-[#8893AC] tracking-wide md:pb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="size-6 fill-[#FF932F] mr-1 pb-1">
                    <path
                      fillRule="evenodd"
                      d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Discover your intelligence level
                </li>
                <li className="flex text-[#8893AC] tracking-wide md:pb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="size-6 fill-[#FF932F] mr-1 pb-1">
                    <path
                      fillRule="evenodd"
                      d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Compare your abilities with others
                </li>
                <li className="flex text-[#8893AC] tracking-wide md:pb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="size-6 fill-[#FF932F] mr-1 pb-1">
                    <path
                      fillRule="evenodd"
                      d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Identify your strengths and weaknesses
                </li>
              </ul>
              <button
                onClick={scrollToElement}
                className="text-white hidden md:block mt-2 md:mt-8 text-[20px] px-8 py-4 rounded-md bg-[#FF932F] w-full md:w-auto font-semibold">
                Get My IQ Score Now!
              </button>
              <button
                onClick={scrollToElementMobile}
                className="text-white block md:hidden mt-2 md:mt-8 text-[20px] px-8 py-4 rounded-md bg-[#FF932F] w-full md:w-auto font-semibold">
                Get My IQ Score Now!
              </button>
            </div>
          </div>
          <div className="max-w-[1275px] mx-auto">
            <PauseOnHover />
          </div>
        </div>
        <div className="bg-white">
          <div ref={scroll} className="max-w-[1056px] mx-auto md:flex px-3 md:px-0 py-6 md:py-16">
            <div className="text-[#191919] md:w-1/2">
              <p className="text-[#FF932F] text-[16px] bg-[#FF932F1A] max-w-[378px] font-bold p-1 pt-2 text-center mb-2">
                Over 3492 tests taken today Avg. IQ score: 103
              </p>
              <h4 className=" text-center md:text-left text-[20px] md:text-[26px] font-bold pb-2 md:pb-5">
                Start your journey with us
              </h4>
              <div className="bg-[#C1CFE91A] text-[16px] p-2 pt-4 md:pr-12 rounded-lg">
                <ul>
                  <li className="flex pb-3 ml-3">
                    <div className="mr-3">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="10" fill="#FF932F" />
                        <path
                          d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                          fill="white"
                        />
                      </svg>
                    </div>

                    <p>
                      Receive your accurate <b>IQ score</b> through our scientifically validated assessment
                    </p>
                  </li>
                  <li className="flex pb-3 ml-3">
                    <div className="mr-3">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="10" fill="#FF932F" />
                        <path
                          d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                          fill="white"
                        />
                      </svg>
                    </div>

                    <p>
                      <b>Understand</b> your <b>position</b> relative to the general population
                    </p>
                  </li>
                  <li className="flex pb-3 ml-3">
                    <div className="mr-3">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="10" fill="#FF932F" />
                        <path
                          d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                          fill="white"
                        />
                      </svg>
                    </div>

                    <p>
                      Discover your cognitive <b>strengths</b> and areas for <b>improvement </b>
                    </p>
                  </li>

                  <li className="flex pb-3 ml-3">
                    <div className="mr-3">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="10" fill="#FF932F" />
                        <path
                          d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                          fill="white"
                        />
                      </svg>
                    </div>
                    <p>
                      Benefit from a personalized training program proven to <b>increase IQ</b> by up to <b>37%</b> in
                      just 4 weeks
                    </p>
                  </li>
                </ul>
              </div>
              <button
                onClick={() => {
                  router.push('/mobile-checkout');
                }}
                className="block md:hidden bg-[#FF932F] py-3.5 flex w-[95%] mx-auto rounded-md text-center mt-4">
                <div className="flex w-[200px] m-auto text-white font-bold text-[20px]">
                  <svg
                    width="12"
                    height="17"
                    viewBox="0 0 12 17"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="mt-1 mr-4">
                    <path
                      d="M6.04549 9.29492C5.77629 9.29696 5.51553 9.38908 5.30479 9.55659C5.09406 9.72411 4.94548 9.95736 4.88277 10.2192C4.82005 10.481 4.84681 10.7562 4.95876 11.001C5.07072 11.2459 5.26144 11.4461 5.50049 11.5699V12.9999H6.50049V11.6149C6.75866 11.5091 6.97211 11.317 7.10442 11.0714C7.23674 10.8257 7.27972 10.5418 7.22602 10.268C7.17232 9.99422 7.02528 9.74755 6.80999 9.57009C6.5947 9.39263 6.32449 9.29537 6.04549 9.29492Z"
                      fill="white"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M11.6189 6.3787H10.1189V4.35986C10.1407 3.24451 9.71993 2.16589 8.94862 1.35988C8.17704 0.553574 7.11749 0.0855945 6.00181 0.0585938L5.996 0.0587344C4.88033 0.0857351 3.82077 0.553574 3.04919 1.35988C2.27788 2.16589 1.85713 3.24452 1.87891 4.35988V6.3787H0.378906V14.9987C0.378906 15.2957 0.496906 15.5806 0.706947 15.7907C0.916988 16.0007 1.20186 16.1187 1.49891 16.1187H10.4989C10.7959 16.1187 11.0808 16.0007 11.2909 15.7907C11.5009 15.5806 11.6189 15.2957 11.6189 14.9987V6.3787ZM8.96045 1.54952C9.64672 2.31994 10.0186 3.32259 9.99805 4.35773V6.49774L9.99891 4.3587C10.0194 3.32312 9.64727 2.32007 8.96045 1.54952ZM11.498 6.4987V14.9977C11.498 15.263 11.3927 15.5173 11.2052 15.7048C11.0176 15.8924 10.7633 15.9977 10.498 15.9977H1.49805C1.23283 15.9977 0.978476 15.8924 0.79094 15.7048C0.777175 15.6911 0.763852 15.677 0.750982 15.6625C0.764118 15.6773 0.777728 15.6917 0.791799 15.7058C0.979336 15.8933 1.23369 15.9987 1.49891 15.9987H10.4989C10.7641 15.9987 11.0185 15.8933 11.206 15.7058C11.3935 15.5183 11.4989 15.2639 11.4989 14.9987L11.498 6.4987ZM3.11891 6.3787V4.3587L3.11886 4.35554C3.09814 3.56836 3.38983 2.80497 3.9302 2.23218C4.46969 1.66032 5.21326 1.32486 5.99891 1.29876C6.78455 1.32486 7.52812 1.66032 8.06761 2.23218C8.60798 2.80497 8.89963 3.56836 8.87891 4.35554V6.3787H3.11891ZM3.84291 2.14984C4.40504 1.55398 5.18016 1.20484 5.99891 1.1787C6.81765 1.20484 7.59277 1.55398 8.1549 2.14984C8.17561 2.17179 8.19597 2.19402 8.21598 2.2165C8.1957 2.19369 8.17505 2.17114 8.15404 2.14887C7.59191 1.55302 6.81679 1.20388 5.99805 1.17773C5.1793 1.20388 4.40418 1.55302 3.84205 2.14887C3.27993 2.74473 2.97649 3.53885 2.99805 4.35773V6.49774L2.99891 4.3587C2.97735 3.53982 3.28078 2.74569 3.84291 2.14984ZM1.61891 14.8787V7.6187H10.3789V14.8787H1.61891ZM1.49891 14.9977L1.49805 7.49774H10.498L1.49891 7.4987V14.9977Z"
                      fill="white"
                    />
                  </svg>
                  Get your IQ Score
                </div>
              </button>
              <div className="block md:hidden text-[#8893AC] text-[16px] mt-3">
                <div className="flex m-auto w-[350px] font-light">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="mr-1 mt-0.5">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M11.3238 6.50654H12.6571V14.1688C12.6571 14.4328 12.5522 14.686 12.3655 14.8727C12.1788 15.0594 11.9256 15.1643 11.6616 15.1643H3.66157C3.39753 15.1643 3.14431 15.0594 2.95761 14.8727C2.7709 14.686 2.66602 14.4328 2.66602 14.1688V6.50654H3.99935V4.71204C3.97999 3.72061 4.35399 2.76183 5.0396 2.04537C5.72545 1.32865 6.66728 0.912798 7.65899 0.888797L7.66416 0.888672C8.65587 0.912673 9.59769 1.32865 10.2835 2.04537C10.9691 2.76182 11.3431 3.7206 11.3238 4.71202V6.50654ZM11.2164 4.71013C11.2346 3.79 10.9041 2.89876 10.2941 2.21394C10.9046 2.89887 11.2354 3.79048 11.2171 4.71099L11.2164 6.61235V4.71013ZM12.5497 14.1679V6.61321L12.5505 14.1688C12.5505 14.4045 12.4568 14.6306 12.2901 14.7973C12.1234 14.964 11.8973 15.0577 11.6616 15.0577H3.66157C3.42582 15.0577 3.19973 14.964 3.03303 14.7973C3.02052 14.7848 3.00843 14.772 2.99675 14.7588C3.00819 14.7717 3.02003 14.7842 3.03227 14.7964C3.19897 14.9631 3.42506 15.0568 3.66081 15.0568H11.6608C11.8966 15.0568 12.1226 14.9631 12.2893 14.7964C12.456 14.6298 12.5497 14.4037 12.5497 14.1679ZM5.10157 4.71099V6.50654H10.2216V4.70818C10.24 4.00846 9.98075 3.3299 9.50042 2.82075C9.02087 2.31243 8.35992 2.01424 7.66157 1.99104C6.96322 2.01424 6.30227 2.31243 5.82272 2.82075C5.34239 3.3299 5.08311 4.00846 5.10153 4.70818L5.10157 4.71099ZM7.66157 1.88432C6.9338 1.90756 6.2448 2.21791 5.74513 2.74756C5.24546 3.2772 4.97574 3.98309 4.9949 4.71099L4.99414 6.61235V4.71013C4.97498 3.98224 5.2447 3.27635 5.74437 2.7467C6.24404 2.21705 6.93303 1.9067 7.66081 1.88346C8.38858 1.9067 9.07758 2.21705 9.57725 2.7467C9.59592 2.76649 9.61427 2.78653 9.6323 2.80681C9.61452 2.78682 9.59642 2.76707 9.57801 2.74756C9.07834 2.21791 8.38935 1.90756 7.66157 1.88432Z"
                      fill="#8893AC"
                    />
                    <path
                      d="M7.70363 9.09961C7.46434 9.10142 7.23256 9.18331 7.04523 9.33221C6.85791 9.48111 6.72585 9.68845 6.6701 9.92115C6.61435 10.1539 6.63813 10.3985 6.73765 10.6162C6.83717 10.8338 7.00669 11.0118 7.21918 11.1218V12.3929H8.10807V11.1618C8.33756 11.0678 8.52729 10.897 8.6449 10.6787C8.76252 10.4603 8.80072 10.2079 8.75299 9.96458C8.70526 9.72121 8.57456 9.50195 8.38319 9.3442C8.19181 9.18646 7.95163 9.10001 7.70363 9.09961Z"
                      fill="white"
                    />
                  </svg>
                  All transactions are secure and encrypted
                </div>
              </div>
              <div ref={scroll2} className="flex justify-between pt-5 pb-3 border-b-2">
                <h5 className="text-[20px] font-bold pt-6">Total due today:</h5>
                <div>
                  <p className="text-[#FF932F] font-bold text-[14px] text-right">
                    You save {Math.round((1 - prices.oneTime.amount / prices.subscription.amount) * 100)}%
                  </p>

                  <p className="text-[20px] font-bold">
                    <span className="text-[#8893AC] text-[14px] pr-2 line-through">
                      {prices.subscription.formatted}
                    </span>
                    €0.70
                  </p>
                </div>
              </div>
              <div className="bg-[#FFF4EA] flex mt-2">
                <svg
                  width="30"
                  height="32"
                  viewBox="0 0 30 32"
                  fill="none"
                  className="m-3"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M26.875 2.75H23.3125V1.5625C23.3125 1.24756 23.1874 0.94551 22.9647 0.722811C22.742 0.500111 22.4399 0.375 22.125 0.375C21.8101 0.375 21.508 0.500111 21.2853 0.722811C21.0626 0.94551 20.9375 1.24756 20.9375 1.5625V2.75H9.0625V1.5625C9.0625 1.24756 8.93739 0.94551 8.71469 0.722811C8.49199 0.500111 8.18994 0.375 7.875 0.375C7.56006 0.375 7.25801 0.500111 7.03531 0.722811C6.81261 0.94551 6.6875 1.24756 6.6875 1.5625V2.75H3.125C2.49511 2.75 1.89102 3.00022 1.44562 3.44562C1.00022 3.89102 0.75 4.49511 0.75 5.125V28.875C0.75 29.5049 1.00022 30.109 1.44562 30.5544C1.89102 30.9998 2.49511 31.25 3.125 31.25H26.875C27.5049 31.25 28.109 30.9998 28.5544 30.5544C28.9998 30.109 29.25 29.5049 29.25 28.875V5.125C29.25 4.49511 28.9998 3.89102 28.5544 3.44562C28.109 3.00022 27.5049 2.75 26.875 2.75ZM19.4027 23.2848C19.513 23.3952 19.6005 23.5262 19.6602 23.6703C19.7199 23.8145 19.7507 23.969 19.7507 24.125C19.7507 24.281 19.7199 24.4355 19.6602 24.5797C19.6005 24.7238 19.513 24.8548 19.4027 24.9652C19.2923 25.0755 19.1613 25.163 19.0172 25.2227C18.873 25.2824 18.7185 25.3132 18.5625 25.3132C18.4065 25.3132 18.252 25.2824 18.1078 25.2227C17.9637 25.163 17.8327 25.0755 17.7223 24.9652L15 22.2413L12.2777 24.9652C12.1673 25.0755 12.0363 25.163 11.8922 25.2227C11.748 25.2824 11.5935 25.3132 11.4375 25.3132C11.2815 25.3132 11.127 25.2824 10.9828 25.2227C10.8387 25.163 10.7077 25.0755 10.5973 24.9652C10.487 24.8548 10.3995 24.7238 10.3398 24.5797C10.2801 24.4355 10.2493 24.281 10.2493 24.125C10.2493 23.969 10.2801 23.8145 10.3398 23.6703C10.3995 23.5262 10.487 23.3952 10.5973 23.2848L13.3212 20.5625L10.5973 17.8402C10.3745 17.6173 10.2493 17.3151 10.2493 17C10.2493 16.6849 10.3745 16.3827 10.5973 16.1598C10.8202 15.937 11.1224 15.8118 11.4375 15.8118C11.7526 15.8118 12.0548 15.937 12.2777 16.1598L15 18.8837L17.7223 16.1598C17.8327 16.0495 17.9637 15.962 18.1078 15.9023C18.252 15.8426 18.4065 15.8118 18.5625 15.8118C18.7185 15.8118 18.873 15.8426 19.0172 15.9023C19.1613 15.962 19.2923 16.0495 19.4027 16.1598C19.513 16.2702 19.6005 16.4012 19.6602 16.5453C19.7199 16.6895 19.7507 16.844 19.7507 17C19.7507 17.156 19.7199 17.3105 19.6602 17.4547C19.6005 17.5988 19.513 17.7298 19.4027 17.8402L16.6788 20.5625L19.4027 23.2848ZM26.875 9.875H3.125V5.125H6.6875V6.3125C6.6875 6.62744 6.81261 6.92949 7.03531 7.15219C7.25801 7.37489 7.56006 7.5 7.875 7.5C8.18994 7.5 8.49199 7.37489 8.71469 7.15219C8.93739 6.92949 9.0625 6.62744 9.0625 6.3125V5.125H20.9375V6.3125C20.9375 6.62744 21.0626 6.92949 21.2853 7.15219C21.508 7.37489 21.8101 7.5 22.125 7.5C22.4399 7.5 22.742 7.37489 22.9647 7.15219C23.1874 6.92949 23.3125 6.62744 23.3125 6.3125V5.125H26.875V9.875Z"
                    fill="#FF932F"
                  />
                </svg>
                <div>
                  <p className="text-[#191919] text-[14px] font-bold pt-2">No commitment</p>
                  <p className="text-[#191919B2] text-[14px]">Cancel anytime</p>
                </div>
              </div>
              <div className="bg-[#FFF4EA] flex mt-2">
                <svg
                  width="30"
                  height="31"
                  viewBox="0 0 30 31"
                  fill="none"
                  className="m-3"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M26.875 0.9375H3.125C2.49511 0.9375 1.89102 1.18772 1.44562 1.63312C1.00022 2.07852 0.75 2.68261 0.75 3.3125V11.625C0.75 19.4506 4.53813 24.1932 7.71617 26.7938C11.1391 29.5934 14.5443 30.5434 14.6927 30.5834C14.8968 30.639 15.1121 30.639 15.3162 30.5834C15.4646 30.5434 18.8653 29.5934 22.2927 26.7938C25.4619 24.1932 29.25 19.4506 29.25 11.625V3.3125C29.25 2.68261 28.9998 2.07852 28.5544 1.63312C28.109 1.18772 27.5049 0.9375 26.875 0.9375ZM21.7806 11.2777L13.4681 19.5902C13.3578 19.7006 13.2269 19.7882 13.0827 19.8479C12.9386 19.9077 12.784 19.9384 12.628 19.9384C12.4719 19.9384 12.3174 19.9077 12.1732 19.8479C12.0291 19.7882 11.8981 19.7006 11.7878 19.5902L8.22531 16.0277C8.00249 15.8048 7.87731 15.5026 7.87731 15.1875C7.87731 14.8724 8.00249 14.5702 8.22531 14.3473C8.44814 14.1245 8.75035 13.9993 9.06547 13.9993C9.38059 13.9993 9.6828 14.1245 9.90563 14.3473L12.625 17.0712L20.0973 9.59734C20.2077 9.48701 20.3387 9.39949 20.4828 9.33978C20.627 9.28007 20.7815 9.24934 20.9375 9.24934C21.0935 9.24934 21.248 9.28007 21.3922 9.33978C21.5363 9.39949 21.6673 9.48701 21.7777 9.59734C21.888 9.70767 21.9755 9.83866 22.0352 9.98281C22.0949 10.127 22.1257 10.2815 22.1257 10.4375C22.1257 10.5935 22.0949 10.748 22.0352 10.8922C21.9755 11.0363 21.888 11.1673 21.7777 11.2777H21.7806Z"
                    fill="#FF932F"
                  />
                </svg>
                <div>
                  <p className="text-[#191919] text-[14px] font-bold pt-2">30-day money-back guarantee</p>
                  <p className="text-[#191919B2] text-[14px]">We will remind you 2 days before your trial ends</p>
                </div>
              </div>
              <p className="text-[14px] py-2 text-left">
                Our IQ test service is exceptionally offered for only {prices.oneTime.formatted} for a 2-day trial.
                Afterwards, it be {prices.subscription.formatted} per month
              </p>
            </div>

            <div className="hidden md:block md:w-1/2 md:ml-16">
              <div className="py-4">
                {paymentSystem ? <PaymentGateway paymentSystem={paymentSystem} isTrial={isTrial} /> : null}
              </div>
              <div className="text-[#8893AC] text-[16px] mt-3">
                <div className="flex m-auto w-[350px] font-light">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="mr-1 mt-0.5">
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M11.3238 6.50654H12.6571V14.1688C12.6571 14.4328 12.5522 14.686 12.3655 14.8727C12.1788 15.0594 11.9256 15.1643 11.6616 15.1643H3.66157C3.39753 15.1643 3.14431 15.0594 2.95761 14.8727C2.7709 14.686 2.66602 14.4328 2.66602 14.1688V6.50654H3.99935V4.71204C3.97999 3.72061 4.35399 2.76183 5.0396 2.04537C5.72545 1.32865 6.66728 0.912798 7.65899 0.888797L7.66416 0.888672C8.65587 0.912673 9.59769 1.32865 10.2835 2.04537C10.9691 2.76182 11.3431 3.7206 11.3238 4.71202V6.50654ZM11.2164 4.71013C11.2346 3.79 10.9041 2.89876 10.2941 2.21394C10.9046 2.89887 11.2354 3.79048 11.2171 4.71099L11.2164 6.61235V4.71013ZM12.5497 14.1679V6.61321L12.5505 14.1688C12.5505 14.4045 12.4568 14.6306 12.2901 14.7973C12.1234 14.964 11.8973 15.0577 11.6616 15.0577H3.66157C3.42582 15.0577 3.19973 14.964 3.03303 14.7973C3.02052 14.7848 3.00843 14.772 2.99675 14.7588C3.00819 14.7717 3.02003 14.7842 3.03227 14.7964C3.19897 14.9631 3.42506 15.0568 3.66081 15.0568H11.6608C11.8966 15.0568 12.1226 14.9631 12.2893 14.7964C12.456 14.6298 12.5497 14.4037 12.5497 14.1679ZM5.10157 4.71099V6.50654H10.2216V4.70818C10.24 4.00846 9.98075 3.3299 9.50042 2.82075C9.02087 2.31243 8.35992 2.01424 7.66157 1.99104C6.96322 2.01424 6.30227 2.31243 5.82272 2.82075C5.34239 3.3299 5.08311 4.00846 5.10153 4.70818L5.10157 4.71099ZM7.66157 1.88432C6.9338 1.90756 6.2448 2.21791 5.74513 2.74756C5.24546 3.2772 4.97574 3.98309 4.9949 4.71099L4.99414 6.61235V4.71013C4.97498 3.98224 5.2447 3.27635 5.74437 2.7467C6.24404 2.21705 6.93303 1.9067 7.66081 1.88346C8.38858 1.9067 9.07758 2.21705 9.57725 2.7467C9.59592 2.76649 9.61427 2.78653 9.6323 2.80681C9.61452 2.78682 9.59642 2.76707 9.57801 2.74756C9.07834 2.21791 8.38935 1.90756 7.66157 1.88432Z"
                      fill="#8893AC"
                    />
                    <path
                      d="M7.70363 9.09961C7.46434 9.10142 7.23256 9.18331 7.04523 9.33221C6.85791 9.48111 6.72585 9.68845 6.6701 9.92115C6.61435 10.1539 6.63813 10.3985 6.73765 10.6162C6.83717 10.8338 7.00669 11.0118 7.21918 11.1218V12.3929H8.10807V11.1618C8.33756 11.0678 8.52729 10.897 8.6449 10.6787C8.76252 10.4603 8.80072 10.2079 8.75299 9.96458C8.70526 9.72121 8.57456 9.50195 8.38319 9.3442C8.19181 9.18646 7.95163 9.10001 7.70363 9.09961Z"
                      fill="white"
                    />
                  </svg>
                  All transactions are secure and encrypted
                </div>
              </div>
              <Image src="/images/secure.svg" width={79} height={32} className="m-auto mt-5 bg-[#F6F9FF]" alt={''} />
            </div>
          </div>

          <div className="max-w-[1276px] mx-auto px-3 md:px-0 pb-8 pt-8 md:pt-0">
            <div className="md:flex">
              <h2 className="text-[38px] md:text-[52px] md:w-2/5 font-bold tracking-tight leading-[56px] text-[#191919]">
                Why You Can Trust <br /> IQ International
              </h2>
              <p className="hidden md:block md:w-3/5 text-[#8893AC] font-[400] md:pl-12 leading-[26px] text-[18px]">
                IQ International is a trusted leader in cognitive development, offering accurate, research-backed
                content. With expert guidance and a proven track record, we ensure reliable, user-focused courses to
                help you reach your intellectual potential. Choose us for a clear path to smarter thinking
              </p>
            </div>
            <div className="md:flex md:py-10">
              <div className="bg-[#E7F6EF] py-7 px-6 md:w-1/3">
                <svg width="28" height="69" viewBox="0 0 28 69" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M27.7827 68.4996H16.1256V17.8885C12.9199 22.357 7.77138 24.6884 0.777138 24.6884H0V13.5171H0.777138C10.8799 13.5171 16.2228 9.33995 16.2228 1.47142V0.5H27.7827V68.4996Z"
                    fill="url(#paint0_linear_1810_4866)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_1810_4866"
                      x1="13.8913"
                      y1="0.5"
                      x2="13.8913"
                      y2="68.4996"
                      gradientUnits="userSpaceOnUse">
                      <stop stopColor="#13A768" />
                      <stop offset="1" stopColor="#13A768" stopOpacity="0.25" />
                    </linearGradient>
                  </defs>
                </svg>

                <h4 className="pt-6 pb-2 text-[#191919] text-[24px] font-bold">Validated IQ Test</h4>
                <p className="text-[#8893AC] font-[400] leading-[26px] text-[16px]">
                  Our test is based on the Stanford-Binet Intelligence Scale, since 1916, it has been the gold standard
                  in the IQ testing industry
                </p>
              </div>
              <div className="bg-[#EBF8FF] py-7 my-4 md:my-0 px-6 md:w-1/3 md:mx-5">
                <svg width="48" height="69" viewBox="0 0 48 69" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M47.0655 20.7573C47.0655 42.7266 17.2446 42.7266 14.4817 58.2287H45.8269V68.5H1.52439V62.2231C1.52439 36.735 35.442 37.9713 35.442 21.1378C35.442 14.9559 31.1547 10.7713 24.5808 10.7713C17.6257 10.7713 12.1951 15.4315 12.0046 23.9909H0C1.04802 8.96434 11.3376 0.5 24.7713 0.5C38.1097 0.5 47.0655 8.67902 47.0655 20.7573Z"
                    fill="url(#paint0_linear_1810_4872)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_1810_4872"
                      x1="23.5327"
                      y1="0.5"
                      x2="23.5327"
                      y2="68.5"
                      gradientUnits="userSpaceOnUse">
                      <stop stopColor="#39BAFF" />
                      <stop offset="1" stopColor="#39BAFF" stopOpacity="0.25" />
                    </linearGradient>
                  </defs>
                </svg>

                <h4 className="pt-6 pb-2 text-[#191919] text-[24px] font-bold">Comprehensive Report</h4>
                <p className="text-[#8893AC] font-[400] leading-[26px] text-[16px]">
                  We generate you custom report with a widely accepted method - the Cattell-Horn-Carroll (CHC) theory of
                  cognitive abilities.
                </p>
              </div>
              <div className="bg-[#FFF4EA] py-7 px-6 md:w-1/3">
                <svg width="51" height="69" viewBox="0 0 51 69" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M26.7147 68.5C11.3024 68.5 0.747264 60.0349 0 46.2674H11.4892C12.143 53.8023 18.0277 58.3605 26.7147 58.3605C34.7478 58.3605 39.7918 54.3605 39.7918 48.2209C39.7918 42.1744 34.8412 38.4535 26.6213 38.4535H16.4398V29.2442H26.6213C33.2532 29.2442 37.2698 25.0581 37.2698 19.9419C37.2698 14.5465 32.8796 10.5465 26.3411 10.5465C19.7091 10.5465 14.6651 14.7326 13.8244 21.1512H2.24179C3.5495 8.68605 13.2639 0.5 26.7147 0.5C39.4182 0.5 48.4788 7.75582 48.4788 17.8954C48.4788 25.6163 43.1545 31.5698 35.3082 33.2442C44.9293 35.0116 50.7205 41.1512 50.7205 49.8023C50.7205 60.8721 41.1929 68.5 26.7147 68.5Z"
                    fill="url(#paint0_linear_1810_4878)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_1810_4878"
                      x1="25.3603"
                      y1="0.5"
                      x2="25.3603"
                      y2="68.5"
                      gradientUnits="userSpaceOnUse">
                      <stop stopColor="#FF932F" />
                      <stop offset="1" stopColor="#FF932F" stopOpacity="0.25" />
                    </linearGradient>
                  </defs>
                </svg>

                <h4 className="pt-6 pb-2 text-[#191919] text-[24px] font-bold">Neuroscience-backed training</h4>
                <p className="text-[#8893AC] font-[400] leading-[26px] text-[16px]">
                  Our neuroscience-based cognitive training boosts mental performance and IQ with quick, measurable
                  results and simple steps
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="md:max-w-[1276px] m-auto py-6 px-3 md:px-0">
          <h1 className="text-[38px] md:text-[52px] text-center text-[#191919] font-bold tracking-tight leading-[56px] py-10">
            How Improving Your IQ Score <br className="hidden md:block" />
            Can Impact Your Life
          </h1>
          <div className="md:flex md:space-x-6 md:mb-10">
            <ImproveCard
              imgSource="improve_1"
              header="Confident solutions"
              text="Tackle complex problems with clarity 
  and confidence"
            />
            <ImproveCard
              imgSource="improve_2"
              header="Surpass competitors"
              text="Excel in competitive environments 
  and outperform others"
            />
            <ImproveCard
              imgSource="improve_3"
              header="Skill mastery"
              text="Master new skills quickly and retain 
  information longer"
            />
          </div>
          <div className="md:flex md:space-x-6 md:mb-10">
            <ImproveCard
              imgSource="improve_4"
              header="Career advancement"
              text="Unlock new career paths and achieve your professional goals"
            />
            <ImproveCard
              imgSource="improve_5"
              header="Smart choices"
              text="Make better decisions in every aspect
  of your life"
            />
            <ImproveCard
              imgSource="improve_6"
              header="Confidently conquer"
              text="Boost your confidence to take on new challenges with ease"
            />
          </div>
        </div>
        <div className=" bg-white">
          <div className="m-auto max-w-full md:max-w-[1276px] pt-12 pb-12  md:pt-16 md:pb-24 relative ">
            <Suspense fallback={<div>Loading Reviews...</div>}>
              <ReviewsCarouselCheckout />
            </Suspense>
          </div>
        </div>
        <div>
          <div className="max-w-[1276px] px-3 md:px-0 mx-auto py-10 md:pt-16 md:pb-16">
            <div className="md:flex">
              <h2 className="text-[38px] md:text-[52px] md:w-2/5 font-bold tracking-tight pb-5 md:pb-5 leading-[56px] text-[#191919]">
                Frequently Asked Questions
              </h2>
              <div className="md:w-3/5 text-[#8893AC] font-[400] md:pl-12 leading-[26px] text-[18px]">
                <AccordionCheckout
                  title="What is included?"
                  content="The program includes a comprehensive IQ test designed by experts, followed by a detailed personal report that breaks down your cognitive strengths and weaknesses. You'll also get personalized tips for improving cognitive performance and access to advanced tools for brain training."
                />
                <AccordionCheckout
                  title="What if I'm not satisfied with the program?"
                  content="We offer a satisfaction guarantee, as well as a 30-day money-back guarantee. If you're not happy with your experience, you can contact our support team within the first 7 days of your trial, and we’ll be happy to assist you."
                />
                <AccordionCheckout
                  title="How long does it take to see results?"
                  content="You'll receive your test results and comprehensive report immediately after completing the IQ assessment. This includes a full breakdown of your score and cognitive analysis."
                />
                <AccordionCheckout
                  title="Is the IQ assessment scientifically valid?"
                  content="Yes, our IQ test is based on scientifically recognized principles of cognitive testing. It has been developed with the input of psychologists and cognitive scientists to ensure accuracy and reliability."
                />
                <AccordionCheckout
                  title="Can I access the program on multiple devices?"
                  content="Yes, you can access your IQ test and personal report on multiple devices. Simply log in to your account from any internet-connected device, and you will have full access to your program and results."
                />
                <AccordionCheckout
                  title="What if I have additional questions or need support?"
                  content="We're here to help! If you have any additional questions or need support, you can contact us through our support page or email our customer service team directly. We aim to respond within 24 hours."
                />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white">
          <div className="max-w-[1276px] mx-auto pt-16 pb-24">
            <div className="px-5 md:px-0">
              <h1 className="text-[52px] text-center font-bold tracking-tight leading-[56px] pb-20 text-[#191919]">
                Latest results
              </h1>
              <div>
                <div className="md:flex w-full">
                  {peopleArray[0] && (
                    <LatestResultPart
                      image={peopleArray[0].image}
                      name={peopleArray[0].name}
                      iq={peopleArray[0].IQ}
                      mobileColored={false}
                      isFirst={true}
                    />
                  )}
                  {peopleArray[2] && (
                    <LatestResultPart
                      image={peopleArray[2].image}
                      name={peopleArray[2].name}
                      iq={peopleArray[2].IQ}
                      mobileColored={true}
                      isFirst={false}
                    />
                  )}
                </div>
                <div className="md:flex rounded-md md:bg-[#F6F9FF]">
                  {peopleArray[1] && (
                    <LatestResultPart
                      image={peopleArray[1].image}
                      name={peopleArray[1].name}
                      iq={peopleArray[1].IQ}
                      mobileColored={false}
                      isFirst={false}
                    />
                  )}
                  {peopleArray[3] && (
                    <LatestResultPart
                      image={peopleArray[3].image}
                      name={peopleArray[3].name}
                      iq={peopleArray[3].IQ}
                      mobileColored={true}
                      isFirst={false}
                    />
                  )}
                </div>
                <div className="md:flex">
                  {peopleArray[4] && (
                    <LatestResultPart
                      image={peopleArray[4].image}
                      name={peopleArray[4].name}
                      iq={peopleArray[4].IQ}
                      mobileColored={false}
                      isFirst={false}
                      twoMins={true}
                    />
                  )}
                  {peopleArray[6] && (
                    <LatestResultPart
                      image={peopleArray[6].image}
                      name={peopleArray[6].name}
                      iq={peopleArray[6].IQ}
                      mobileColored={true}
                      isFirst={false}
                      twoMins={true}
                    />
                  )}
                </div>
                <div className="md:flex rounded-md md:bg-[#F6F9FF]">
                  {peopleArray[5] && (
                    <LatestResultPart
                      image={peopleArray[5].image}
                      name={peopleArray[5].name}
                      iq={peopleArray[5].IQ}
                      mobileColored={false}
                      isFirst={false}
                      twoMins={true}
                    />
                  )}
                  {peopleArray[7] && (
                    <LatestResultPart
                      image={peopleArray[7].image}
                      name={peopleArray[7].name}
                      iq={peopleArray[7].IQ}
                      mobileColored={true}
                      isFirst={false}
                      twoMins={true}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
