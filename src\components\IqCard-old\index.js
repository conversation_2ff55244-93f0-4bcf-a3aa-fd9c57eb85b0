import Image from 'next/image';

export default function IqCard({ imgSource, name, text }) {
  return (
    <div>
      <div
        style={{
          backgroundImage: `url("/images/${imgSource}.svg")`,
          backgroundPosition: 'bottom',
          backgroundRepeat: 'no-repeat',
        }}
        className={`bg-[#C1CFE943] hidden md:flex  shadow-md rounded h-[348px] md:w-[233px] `}>
        <div className="bg-[#FFFFFF] w-[209px] font-bold h-[44px] rounded mb-3 mx-auto mt-auto">
          <div className="flex justify-between rounded">
            <p className="py-3 px-2 text-[#191919]">{name}</p>
            <p className="py-3 px-2 text-[#FF932F]">{text}</p>
          </div>
        </div>
      </div>
      <div className="block md:hidden">
        <div
          style={{
            backgroundImage: `url("/images/mobile/${imgSource}.png")`,
            backgroundPosition: 'bottom',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'contain',
          }}
          className={`bg-[#C1CFE943] relative mr-2 flex shadow-md rounded h-[160px] w-[100px] `}>
          <div className="bg-[#FFFFFF] mt-auto w-[102px] font-bold h-[22px] rounded mb-3 mx-auto">
            <div className="flex text-[12px] justify-between rounded">
              <p className="p-1 text-[12px] md:text-[16px] text-[#191919]">{name}</p>
              <p className="p-1 text-[12px] md:text-[16px] absolute top-0 right-0 text-[#FF932F]">{text}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
