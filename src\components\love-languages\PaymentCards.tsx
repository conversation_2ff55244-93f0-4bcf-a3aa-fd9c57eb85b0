import Image from 'next/image';
import React from 'react';

type PaymentCardsProps = {
  h_desktop: string;
  h_mobile: string;
  mb_desktop: string;
  mb_mobile: string;
};

const PaymentCards: React.FC<PaymentCardsProps> = ({ mb_desktop, mb_mobile, h_desktop, h_mobile }) => {
  return (
    <div className={`flex items-center gap-[8px] md:gap-[12px] bg-[#FDFEFF] mb-[${mb_mobile}] md:mb-[${mb_desktop}]`}>
      <Image
        src="/images/emotional-intelligence/visa1.svg"
        alt="visa1"
        width={65}
        height={40}
        className={`h-[${h_mobile}] md:h-[${h_desktop}] w-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa2.svg"
        alt="visa2"
        width={65}
        height={40}
        className={`h-[${h_mobile}] md:h-[${h_desktop}] w-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa3.svg"
        alt="visa3"
        width={65}
        height={40}
        className={`h-[${h_mobile}] md:h-[${h_desktop}] w-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa4.svg"
        alt="visa4"
        width={65}
        height={40}
        className={`h-[${h_mobile}] md:h-[${h_desktop}] w-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa5.svg"
        alt="visa5"
        width={65}
        height={40}
        className={`h-[${h_mobile}] md:h-[${h_desktop}] w-auto`}
      />
    </div>
  );
};

export default PaymentCards;
