"use client";
import SessionContext from "@/store/SessionContext";
import { useContext } from "react";

const LegalNotice = () => {
    const { siteConfig } = useContext(SessionContext);
        
    return <div className="flex flex-col justify-center gap-4">
        <h1 className="text-5xl font-bold mb-10">Legal Notice</h1>
        <div className="flex gap-2 text-xl">
            <span className="font-bold">Website URL:</span>
            <span>{siteConfig.domain}</span>
        </div>
        {siteConfig.companyName &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">The website is managed by:</span>
                <span>{siteConfig.companyName}</span>
            </div>
        }
        {siteConfig.companyAddress &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">Registered address:</span>
                <span>{siteConfig.companyAddress}</span>
            </div>
        }
        {siteConfig.supportEmail &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">Contact:</span>
                <span>{siteConfig.supportEmail}</span>
            </div>
        }
        {siteConfig.hostingProvider &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">Hosting provider:</span>
                <span>{siteConfig.hostingProvider}</span>
            </div>
        }
        {siteConfig.euVat &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">EU VAT:</span>
                <span>{siteConfig.euVat}</span>
            </div>
        }
        {siteConfig.companyRegistrationNumber &&
            <div className="flex gap-2 text-xl">
                <span className="font-bold">Company registration number:</span>
                <span>{siteConfig.companyRegistrationNumber}</span>
            </div>
        }
    </div>
};

export default LegalNotice;