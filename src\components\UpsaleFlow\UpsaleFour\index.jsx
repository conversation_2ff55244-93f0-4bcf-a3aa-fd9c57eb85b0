import Image from 'next/image';
import UpsalePath from '@/components/UpsaleFlow/UpselPath';
import { useState } from 'react';
import UpsaleCard from '../UpsaleCard';

export default function UpsaleFour({ handleNext }) {
  const [choose, setChoose] = useState(1);
  return (
    <div>
      <div className="container mx-auto">
        <UpsalePath />
        <div className="text-center text-[52px] text-[#191919] pt-[40px] md:pt-[60px]">
          <h1 className="text-[30px] md:text-[36px]">Are you sure?</h1>
          <p className="md:pt-2 text-[14px]">Don&apos;t miss out on these powerful tools to unlock your potential</p>
          <h3 className="pt-4 text-[20px] md:text-[24px]">What you get</h3>
        </div>
        <div className="pb-2 md:pb-10 md:pt-4">
          <UpsaleCard
            icon={
              <svg
                className="block m-auto"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M16 3C13.4288 3 10.9154 3.76244 8.77759 5.1909C6.63975 6.61935 4.97351 8.64968 3.98957 11.0251C3.00563 13.4006 2.74819 16.0144 3.2498 18.5362C3.75141 21.0579 4.98953 23.3743 6.80762 25.1924C8.6257 27.0105 10.9421 28.2486 13.4638 28.7502C15.9856 29.2518 18.5995 28.9944 20.9749 28.0104C23.3503 27.0265 25.3807 25.3603 26.8091 23.2224C28.2376 21.0846 29 18.5712 29 16C28.9964 12.5533 27.6256 9.24882 25.1884 6.81163C22.7512 4.37445 19.4467 3.00364 16 3ZM16 27C13.8244 27 11.6977 26.3549 9.88873 25.1462C8.07979 23.9375 6.66989 22.2195 5.83733 20.2095C5.00477 18.1995 4.78693 15.9878 5.21137 13.854C5.63581 11.7202 6.68345 9.7602 8.22183 8.22183C9.76021 6.68345 11.7202 5.6358 13.854 5.21136C15.9878 4.78692 18.1995 5.00476 20.2095 5.83733C22.2195 6.66989 23.9375 8.07979 25.1462 9.88873C26.3549 11.6977 27 13.8244 27 16C26.9967 18.9164 25.8367 21.7123 23.7745 23.7745C21.7123 25.8367 18.9164 26.9967 16 27ZM21.5525 9.105L13.5525 13.105C13.3591 13.2022 13.2022 13.3591 13.105 13.5525L9.10501 21.5525C9.02869 21.705 8.99264 21.8745 9.0003 22.0449C9.00795 22.2153 9.05905 22.3808 9.14874 22.5259C9.23843 22.671 9.36373 22.7907 9.51272 22.8736C9.66172 22.9566 9.82946 23.0001 10 23C10.1552 22.9998 10.3084 22.9638 10.4475 22.895L18.4475 18.895C18.6409 18.7978 18.7979 18.6409 18.895 18.4475L22.895 10.4475C22.9894 10.2597 23.0222 10.0469 22.9887 9.83933C22.9552 9.6318 22.8572 9.4401 22.7085 9.29146C22.5599 9.14282 22.3682 9.0448 22.1607 9.01132C21.9531 8.97785 21.7403 9.01063 21.5525 9.105ZM17.25 17.25L12.2363 19.7638L14.75 14.75L19.7688 12.2413L17.25 17.25Z"
                  class="fill-primary"
                />
              </svg>
            }
            header="Career & Education IQ Guide"
            text="Plan for Success: Discover strategies to fast-track 
          your career and personal growth"
          />
          <UpsaleCard
            icon={
              <svg
                className="mx-auto"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M26 10H12V7C12 5.93913 12.4214 4.92172 13.1716 4.17157C13.9217 3.42143 14.9391 3 16 3C17.9213 3 19.65 4.375 20.02 6.19875C20.0749 6.45646 20.2294 6.68207 20.4497 6.82655C20.6701 6.97103 20.9385 7.0227 21.1968 6.97032C21.455 6.91795 21.6822 6.76577 21.8288 6.54686C21.9755 6.32795 22.0298 6.06 21.98 5.80125C21.415 3.01875 18.9 1 16 1C14.4092 1.00165 12.884 1.63433 11.7592 2.75919C10.6343 3.88405 10.0017 5.40921 10 7V10H6C5.46957 10 4.96086 10.2107 4.58579 10.5858C4.21071 10.9609 4 11.4696 4 12V26C4 26.5304 4.21071 27.0391 4.58579 27.4142C4.96086 27.7893 5.46957 28 6 28H26C26.5304 28 27.0391 27.7893 27.4142 27.4142C27.7893 27.0391 28 26.5304 28 26V12C28 11.4696 27.7893 10.9609 27.4142 10.5858C27.0391 10.2107 26.5304 10 26 10ZM6 16H26V18H6V16ZM6 20H26V22H6V20ZM26 12V14H6V12H26ZM26 26H6V24H26V26Z"
                  class="fill-primary"
                />
              </svg>
            }
            header="Stress Management Secrets"
            text="Achieve Calm and Focus: Master techniques to reduce stress and boost productivity"
          />
          <UpsaleCard
            icon={
              <svg
                className="mx-auto stroke-primary"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M9.33677 6.0013C8.62743 6.0011 7.9308 6.18953 7.31828 6.54727C6.70576 6.90501 6.19939 7.4192 5.85108 8.03713C5.50276 8.65506 5.32503 9.3545 5.33611 10.0638C5.34718 10.773 5.54666 11.4665 5.9141 12.0733C5.00149 12.2496 4.17873 12.7382 3.5871 13.4551C2.99547 14.172 2.67188 15.0725 2.67188 16.002C2.67188 16.9315 2.99547 17.832 3.5871 18.5489C4.17873 19.2657 5.00149 19.7543 5.9141 19.9306M9.33677 6.0013C9.33677 5.11725 9.68795 4.2694 10.3131 3.64428C10.9382 3.01916 11.786 2.66797 12.6701 2.66797C13.5542 2.66797 14.402 3.01916 15.0271 3.64428C15.6522 4.2694 16.0034 5.11725 16.0034 6.0013V26.0013C16.0034 26.8854 15.6522 27.7332 15.0271 28.3583C14.402 28.9834 13.5542 29.3346 12.6701 29.3346C11.786 29.3346 10.9382 28.9834 10.3131 28.3583C9.68795 27.7332 9.33677 26.8854 9.33677 26.0013C8.62763 26.0014 7.93123 25.8129 7.3189 25.4552C6.70658 25.0975 6.20034 24.5835 5.85207 23.9658C5.5038 23.3481 5.326 22.6489 5.3369 21.9398C5.3478 21.2308 5.547 20.5374 5.9141 19.9306M9.33677 6.0013C9.33677 7.09197 9.86077 8.05997 10.6701 8.66797M5.9141 19.9306C6.3926 19.1385 7.13099 18.5367 8.00343 18.228M25.6701 6.33464L22.6701 9.33464H20.0034M25.6701 25.668L22.6701 22.668H20.0034M25.6701 16.0013H20.0034M24.6701 6.33464C24.6701 6.59985 24.7755 6.85421 24.963 7.04174C25.1505 7.22928 25.4049 7.33464 25.6701 7.33464C25.9353 7.33464 26.1897 7.22928 26.3772 7.04174C26.5647 6.85421 26.6701 6.59985 26.6701 6.33464C26.6701 6.06942 26.5647 5.81506 26.3772 5.62753C26.1897 5.43999 25.9353 5.33464 25.6701 5.33464C25.4049 5.33464 25.1505 5.43999 24.963 5.62753C24.7755 5.81506 24.6701 6.06942 24.6701 6.33464ZM24.6701 25.668C24.6701 25.4028 24.7755 25.1484 24.963 24.9609C25.1505 24.7733 25.4049 24.668 25.6701 24.668C25.9353 24.668 26.1897 24.7733 26.3772 24.9609C26.5647 25.1484 26.6701 25.4028 26.6701 25.668C26.6701 25.9332 26.5647 26.1875 26.3772 26.3751C26.1897 26.5626 25.9353 26.668 25.6701 26.668C25.4049 26.668 25.1505 26.5626 24.963 26.3751C24.7755 26.1875 24.6701 25.9332 24.6701 25.668ZM24.6701 16.0013C24.6701 16.2665 24.7755 16.5209 24.963 16.7084C25.1505 16.8959 25.4049 17.0013 25.6701 17.0013C25.9353 17.0013 26.1897 16.8959 26.3772 16.7084C26.5647 16.5209 26.6701 16.2665 26.6701 16.0013C26.6701 15.7361 26.5647 15.4817 26.3772 15.2942C26.1897 15.1067 25.9353 15.0013 25.6701 15.0013C25.4049 15.0013 25.1505 15.1067 24.963 15.2942C24.7755 15.4817 24.6701 15.7361 24.6701 16.0013Z"
                  stroke-width="1.8"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            }
            header="Brain Biohacks Toolkit"
            text="Upgrade Your Mind: Use cutting-edge techniques to improve focus and mental agility"
          />
        </div>
        <div className="flex border-t-2 justify-between">
          <button
            onClick={() => {
              handleNext();
            }}
            className="md:px-8 w-[32%] mx-[1%] md:w-auto py-3 bg-[#E8EDF8] text-black rounded-md md:my-6">
            I Don&apos;t Want to Excel
          </button>
          <button
            onClick={() => {
              handleNext();
            }}
            className="text-white w-[66%] mx-[1%] md:w-[300px] md:px-8 py-3 bg-primary rounded-md md:my-6">
            Return to Toolkits
          </button>
        </div>
      </div>
    </div>
  );
}
