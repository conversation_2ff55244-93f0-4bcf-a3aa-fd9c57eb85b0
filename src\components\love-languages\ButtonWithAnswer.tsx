'use client';

import React from 'react';

interface ButtonWithAnswerProps {
  label: string;
  onClick: () => void;
  isClicked: boolean;
}

const ButtonWithAnswer: React.FC<ButtonWithAnswerProps> = ({ label, onClick, isClicked }) => {
  return (
    <button
      onClick={onClick}
      className={`w-full rounded-[10px] text-left border px-5 py-4 font-raleway text-[16px] md:text-lg font-medium leading-6 active:bg-[#F0F9FF] active:border-[#5DC4FF] ${
        isClicked ? 'bg-[#F0F9FF] border-[#5DC4FF]' : 'bg-white border-[#C1CFE973]'
      }`}>
      {label}
    </button>
  );
};

export default ButtonWithAnswer;
