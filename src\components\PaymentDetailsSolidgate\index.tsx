'use client';

import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';
import CheckoutFormSolidgate from '../CheckoutFormSolidgate';

export default function PaymentSolidgate() {
  const { prices, getIsTrial, merchantData } = useContext(SessionContext);

  return (
    <div id="paymentDetails" className="flex justify-center">
      {merchantData && (
        <div className="flex flex-col">
          <h4 className="text-center md:text-left mb-4 md:mb-6">Payment Details</h4>
          <div className="mx-5">
            <CheckoutFormSolidgate />
            {getIsTrial() ? (
              <p className="text-xs mt-4">
                Our IQ test service is exceptionally offered at the price of {prices.oneTime.formatted}
                {prices.vatIncluded ? ' (incl. VAT)' : ''} for a 2-day trial. At the end of the trial period and without
                cancellation from your side, our offer will be automatically renewed as a non-binding subscription, at
                the price of {prices.subscription.formatted} per month{prices.vatIncluded ? ' (incl. VAT)' : ''}.
              </p>
            ) : (
              <p className="text-xs mt-4">
                Our IQ test service is offered at the price of {prices.subscription.formatted}
                {prices.vatIncluded ? ' (incl. VAT)' : ''} per month as a non-binding subscription.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
