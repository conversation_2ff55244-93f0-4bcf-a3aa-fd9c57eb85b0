import Image from "next/image";

export default function ReviewCard({ name, text, date }) {
  return (
    <div className="bg-white m-auto shadow-md h-[220px] max-w-[412px] my-10 p-5 border mx-3 md:mx-3">
      <svg
        width="106"
        height="18"
        viewBox="0 0 106 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_1810_4452)">
          <path
            d="M97.9793 0.632742C97.7986 0.246066 97.4168 0 96.9976 0C96.5783 0 96.1999 0.246066 96.0158 0.632742L93.824 5.28339L88.9291 6.02862C88.5201 6.0919 88.1792 6.38718 88.0531 6.79143C87.9269 7.19568 88.0292 7.64211 88.3223 7.94091L91.8742 11.5651L91.0357 16.6868C90.9675 17.1086 91.138 17.5375 91.4754 17.7871C91.8129 18.0367 92.2594 18.0683 92.6276 17.8679L97.001 15.46L101.374 17.8679C101.742 18.0683 102.189 18.0402 102.527 17.7871C102.864 17.534 103.034 17.1086 102.966 16.6868L102.124 11.5651L105.676 7.94091C105.969 7.64211 106.075 7.19568 105.945 6.79143C105.816 6.38718 105.478 6.0919 105.069 6.02862L100.171 5.28339L97.9793 0.632742Z"
            class="fill-primary"
          />
        </g>
        <g clip-path="url(#clip1_1810_4452)">
          <path
            d="M9.97926 0.632742C9.7986 0.246066 9.41683 0 8.99755 0C8.57828 0 8.19991 0.246066 8.01584 0.632742L5.82403 5.28339L0.929098 6.02862C0.52005 6.0919 0.179178 6.38718 0.053055 6.79143C-0.0730679 7.19568 0.0291938 7.64211 0.322344 7.94091L3.87424 11.5651L3.03569 16.6868C2.96752 17.1086 3.13795 17.5375 3.47542 17.7871C3.81288 18.0367 4.25942 18.0683 4.62756 17.8679L9.00096 15.46L13.3744 17.8679C13.7425 18.0683 14.189 18.0402 14.5265 17.7871C14.864 17.534 15.0344 17.1086 14.9662 16.6868L14.1243 11.5651L17.6762 7.94091C17.9693 7.64211 18.075 7.19568 17.9455 6.79143C17.8159 6.38718 17.4785 6.0919 17.0694 6.02862L12.1711 5.28339L9.97926 0.632742Z"
            class="fill-primary"
          />
        </g>
        <g clip-path="url(#clip2_1810_4452)">
          <path
            d="M31.9793 0.632742C31.7986 0.246066 31.4168 0 30.9976 0C30.5783 0 30.1999 0.246066 30.0158 0.632742L27.824 5.28339L22.9291 6.02862C22.5201 6.0919 22.1792 6.38718 22.0531 6.79143C21.9269 7.19568 22.0292 7.64211 22.3223 7.94091L25.8742 11.5651L25.0357 16.6868C24.9675 17.1086 25.138 17.5375 25.4754 17.7871C25.8129 18.0367 26.2594 18.0683 26.6276 17.8679L31.001 15.46L35.3744 17.8679C35.7425 18.0683 36.189 18.0402 36.5265 17.7871C36.864 17.534 37.0344 17.1086 36.9662 16.6868L36.1243 11.5651L39.6762 7.94091C39.9693 7.64211 40.075 7.19568 39.9455 6.79143C39.8159 6.38718 39.4785 6.0919 39.0694 6.02862L34.1711 5.28339L31.9793 0.632742Z"
            class="fill-primary"
          />
        </g>
        <g clip-path="url(#clip3_1810_4452)">
          <path
            d="M53.9793 0.632742C53.7986 0.246066 53.4168 0 52.9976 0C52.5783 0 52.1999 0.246066 52.0158 0.632742L49.824 5.28339L44.9291 6.02862C44.5201 6.0919 44.1792 6.38718 44.0531 6.79143C43.9269 7.19568 44.0292 7.64211 44.3223 7.94091L47.8742 11.5651L47.0357 16.6868C46.9675 17.1086 47.138 17.5375 47.4754 17.7871C47.8129 18.0367 48.2594 18.0683 48.6276 17.8679L53.001 15.46L57.3744 17.8679C57.7425 18.0683 58.189 18.0402 58.5265 17.7871C58.864 17.534 59.0344 17.1086 58.9662 16.6868L58.1243 11.5651L61.6762 7.94091C61.9693 7.64211 62.075 7.19568 61.9455 6.79143C61.8159 6.38718 61.4785 6.0919 61.0694 6.02862L56.1711 5.28339L53.9793 0.632742Z"
            class="fill-primary"
          />
        </g>
        <g clip-path="url(#clip4_1810_4452)">
          <path
            d="M75.9793 0.632742C75.7986 0.246066 75.4168 0 74.9976 0C74.5783 0 74.1999 0.246066 74.0158 0.632742L71.824 5.28339L66.9291 6.02862C66.5201 6.0919 66.1792 6.38718 66.0531 6.79143C65.9269 7.19568 66.0292 7.64211 66.3223 7.94091L69.8742 11.5651L69.0357 16.6868C68.9675 17.1086 69.138 17.5375 69.4754 17.7871C69.8129 18.0367 70.2594 18.0683 70.6276 17.8679L75.001 15.46L79.3744 17.8679C79.7425 18.0683 80.189 18.0402 80.5265 17.7871C80.864 17.534 81.0344 17.1086 80.9662 16.6868L80.1243 11.5651L83.6762 7.94091C83.9693 7.64211 84.075 7.19568 83.9455 6.79143C83.8159 6.38718 83.4785 6.0919 83.0694 6.02862L78.1711 5.28339L75.9793 0.632742Z"
            class="fill-primary"
          />
        </g>
        <defs>
          <clipPath id="clip0_1810_4452">
            <rect
              width="18"
              height="18"
              fill="white"
              transform="translate(88)"
            />
          </clipPath>
          <clipPath id="clip1_1810_4452">
            <rect width="18" height="18" fill="white" />
          </clipPath>
          <clipPath id="clip2_1810_4452">
            <rect
              width="18"
              height="18"
              fill="white"
              transform="translate(22)"
            />
          </clipPath>
          <clipPath id="clip3_1810_4452">
            <rect
              width="18"
              height="18"
              fill="white"
              transform="translate(44)"
            />
          </clipPath>
          <clipPath id="clip4_1810_4452">
            <rect
              width="18"
              height="18"
              fill="white"
              transform="translate(66)"
            />
          </clipPath>
        </defs>
      </svg>
      <h2 className="text-[20px] font-bold pt-4 text-[#191919]">{name}</h2>
      <p className="text-[#8893AC] text-[18px] pt-3 pb-5">{text}</p>
      <p className="text-[#8893AC] text-[16px]">{date}</p>
    </div>
  );
}
