"use client";
import { useContext, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import SessionContext from '@/store/SessionContext';

const AboutUs = () => {
  const t = useTranslations('about_us');
  const { siteConfig } = useContext(SessionContext);
  const data = useMemo(
    () => [
      {
        title: t('vision_title'),
        text: [
          t('vision_paragraph1', { siteName: siteConfig.siteName }),
          t('vision_paragraph2', { siteName: siteConfig.siteName }),
        ],
      },
      {
        title: t('mission_title'),
        text: [
          t('mission_paragraph1'),
          t('mission_paragraph2'),
        ],
      },
      {
        title: t('future_title'),
        text: [
          t('future_paragraph1'),
          t('future_paragraph2', { siteName: siteConfig.siteName }),
          t('future_paragraph3'),
        ],
      },
    ],
    [siteConfig, t],
  );

  return (
    <div>
      <section className={`flex flex-wrap justify-between max-w-[1440px] m-auto mt-16 lg:mt-24`} style={{}}>
        <div style={{ maxWidth: 540 }}>
          <h1 className='mb-10 xl:mb-0' style={{ maxWidth: 353 }}>
            {t('page_title')}
          </h1>
        </div>
        <div className='' style={{ maxWidth: 736 }}>
          {data.map((item, index) => (
            <div key={index}>
              <h3 className='big' style={{ marginBottom: 24, marginTop: index > 0 ? 48 : 0 }}>
                {item.title}
              </h3>
              {item.text.map((paragraph, i) => (
                <p key={i} style={{ marginBottom: 16 }}>
                  {paragraph}
                </p>
              ))}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default AboutUs;
