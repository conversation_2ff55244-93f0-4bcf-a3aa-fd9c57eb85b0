import { httpsCallable } from 'firebase/functions';
import { useContext } from 'react';
import { functions } from '@/utils/firebase';
import { addFormDataToSessionDb } from '@/services/session';
import SessionContext from '@/store/SessionContext';  
import { sendGTMEvent } from '@next/third-parties/google';
import { isGTMInitialized } from '@/utils/isGtmInitialized';

const useGetMerchantData = () => {
  const { formData, sessionId, prices, fetchAllSessionData, answers, paymentSystem } = useContext(SessionContext);
  const getMerchantData = async ({
    clickId,
    time,
    isTrial,
  }: {
    clickId: string | null;
    time: number;
    isTrial: boolean;
  }) => {
    const createPaymentIntent = httpsCallable(
      functions,
      paymentSystem === 'solidgate' ? 'createPaymentIntentSolidgate' : 'createPaymentIntent'
    );
    const trackingPostback = httpsCallable(functions, 'trackingPostback');

    const getMerchantDataSolidgate = async () => {
      return createPaymentIntent({
        email: formData.email,
        name: formData.name,
        sessionId,
        clickId,
        landingUrl: localStorage.getItem('landingUrl'),
        country: prices.country,
        completionTime: time,
        checkoutVersion: time < 330 ? 'compliant' : 'v1',
        isTrial,
      })
      .then((res: any) => res.data.merchantData)
      .catch(x => {
        console.log('Error getting merchant data solidgate');
        console.log(x);
      });
    };

    const getMerchantDataStripe = async () => {
      return createPaymentIntent({
        email: formData.email,
        name: formData.name,
        sessionId,
        clickId,
        landingUrl: localStorage.getItem('landingUrl'),
        country: prices.country,
        completionTime: time,
        checkoutVersion: time < 330 ? 'compliant' : 'v1',
        isTrial,
      }).then((res: any) => res.data.clientSecret).catch((x) => {
        console.log('Error getting merchant data stripe');
        console.log(x);
      });
    };
    
    const getMerchantData = paymentSystem === 'solidgate' ? getMerchantDataSolidgate : getMerchantDataStripe;

    const [formAdded, freshMerchantData] = await Promise.all([
      addFormDataToSessionDb({ ...fetchAllSessionData(), time, answers }),
      getMerchantData(),
    ]);

    if (formAdded) {
      if (clickId) trackingPostback({ clickId, event: 'cho' });

      if (!isGTMInitialized()) {
        console.warn('GTM not initialized on results page: formSubmitted event not sent');
        return;
      }

      sendGTMEvent({
        leadsUserData: {
          email: formData.email,
        },
      });
    }

    return freshMerchantData;
  };

  return { getMerchantData };
};

export default useGetMerchantData;
