import { useTranslations } from 'next-intl';
import countries from './countries';
import Image from 'next/image';
import GeoBasedTitle from '@/components/ClientComponents/GeoBasedTitle';
import CtaButton from '@/components/Buttons/CtaButton';
import { ListStyleCheck } from '@/components/Icons/ListStyleCheck';

const HeroSection = () => {
  const t = useTranslations('home_page');
  
  const listContent = [
    { text: t('home_page_checkmark1') },
    { text: t('home_page_checkmark2') },
    { text: t('home_page_checkmark3') },
  ];

  return (
    <section
      style={{
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: '100% 0',
      }}
      className="w-full sm:pb-[150px]"
    >
      <div className='flex flex-wrap justify-between min-h-[540px] pt-[19px] sm:pb-[44px] m-auto 2xl:max-w-[calc(1440px)]'>
        <div className='w-full lg:w-1/2'>
          <GeoBasedTitle {...{ countries }} />
          <h4 className='small'>{t('header_discover_IQ')}</h4>
          <ul style={{ margin: '32px 0 40px 0', color: '#8893AC', fontSize: 18, lineHeight: '111.111%' }}>
            {listContent.map((item, i) => (
              <li
                key={i}
                style={{
                  marginBottom: 13,
                  lineHeight: '23px',
                }}
                className='flex items-center gap-[10px]'
              >
                <ListStyleCheck />
                {item.text}
              </li>
            ))}
          </ul>
          <CtaButton
            className=''
            {...{
              type: 'primary',
              style: {
                paddingLeft: 40,
                paddingRight: 40,
              },
            }}
          />
        </div>
        <div
          className='relative w-full h-full lg:w-1/2 lg:mt-0'
          style={{ background: 'url(/home/<USER>/checkered-min.png)', backgroundSize: 'cover' }}
        >
          <div
            className='relative w-full h-[400px] scale-[0.6] md:scale-75 lg:scale-100'
            style={{
              width: '100%',
            }}
          >
            <Image
              className='z-10 top-[15%] md:top-[calc(50%-160px)] lg:top-[40px] left-[calc(50%-160px)] sm:left-[calc(50%-201px)] lg:left-[calc(50%-101px)]'
              style={{
                zIndex: 1,
                position: 'absolute',
                rotate: '14deg',
                boxShadow: '2px 8px 14px 0px rgba(104, 129, 177, 0.12), 1px 1px 1px 1px rgba(141, 160, 188, 0.08)',
                border: '0.828px solid rgba(193, 207, 233, 0.45)',
              }}
              src={'/home/<USER>/first-card.jpg'}
              quality={100}
              alt='First question example image'
              width={374}
              height={405}
              priority
            />
            <Image
              className='absolute top-[15%] lg:top-[70px] left-[calc(50%-209px)] sm:left-[calc(50%-250px)] lg:left-[calc(50%-150px)]'
              style={{
                rotate: '4.33deg',
                boxShadow: '1px 1px 1px 1px rgba(141, 160, 188, 0.08)',
                border: '0.828px solid rgba(214, 218, 225, 0.45)',
              }}
              src={'/home/<USER>/background-card.jpg'}
              alt='Another example image in the background'
              width={374}
              height={405}
              priority
            />
            <Image
              className='home-hero-wave absolute z-20 top-[calc(50%+210px)] h:top-[calc(50%+150px)] mdh:top-[calc(50%+200px)] md:top-[calc(50%+200px)] lg:top-[360px] left-[calc(50%+60px)] h:left-[calc(50%)] mdh:left-[calc(50%+100px)] sm:left-[calc(50%+20px)] lg:left-[calc(50%-253px)]  scale-75 lg:scale-100 lg:scale-none lg:transform-none'
              src={'/home/<USER>/wave.svg'}
              alt='Small wave for decoration'
              width={204}
              height={140}
              priority
            />
          </div>
          <div
            style={{
              visibility: 'hidden',
              width: '100%',
              top: 0,
              left: 0,
              position: 'relative',
            }}
          >
            1
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
