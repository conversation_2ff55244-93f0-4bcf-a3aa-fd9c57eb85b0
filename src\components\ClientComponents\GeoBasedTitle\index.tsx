'use client';
import { useContext, useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Cookies from 'js-cookie';
import UiContext from '@/store/UiContext';
import SessionContext from '@/store/SessionContext';

const GeoBasedTitle = ({ countries }: { countries: { id: string; iq: number; name: string }[] }) => {
  const t = useTranslations('home_page');
  
  const { completedByNumber } = useContext(UiContext);
  const { locale, updateLocale } = useContext(SessionContext);
  const [number, setNumber] = useState<number>(completedByNumber);

  const country = countries.find((c) => c.id === Cookies.get('locale')?.toLowerCase()) || {
    id: 'us',
    iq: 97.43,
    name: 'United States',
  };

  useEffect(() => {
    updateLocale(Cookies.get('locale') || '');
  }, [locale, updateLocale]);

  useEffect(() => {
    setNumber(completedByNumber);
  }, [completedByNumber]);

  return (
    <div>
      <div style={{ lineHeight: '20px', marginBottom: 26 }}>
        <Image
          style={{ float: 'left', marginRight: 10 }}
          src={`https://flagcdn.com/w80/${country?.id}.png`}
          alt={`${country?.name} flag`}
          width={30}
          height={16}
          priority
          unoptimized
        />
        <span suppressHydrationWarning={true} style={{ fontSize: 16, lineHeight: '125%', color: '#8893AC' }}>
          {t('header_userstoday', { number })}
        </span>
      </div>
      <h1>{t('header_avg_IQ', { country: country?.name, score: country?.iq.toFixed() })}</h1>
    </div>
  );
};

export default GeoBasedTitle;
