import React from 'react';
import Image from 'next/image';

interface StarRatingProps {
  rating: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating }) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, i) => (
        <Image
          key={i}
          width={18}
          height={18}
          className={'w-[16px] md:w-[18px] h-auto'}
          src={
            i < rating
              ? '/images/emotional-intelligence/checkout/star-solid.svg'
              : '/images/emotional-intelligence/checkout/star-empty.svg'
          }
          alt="star rating"
        />
      ))}
    </div>
  );
};

export default StarRating;
