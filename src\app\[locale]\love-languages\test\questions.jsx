export const questions = [
    {
        question: 'I feel most loved when my partner',
        answers: ['Surprises me with comfort food', "Gives a gift that reflects my taste"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Plans a special day for just us', "Notices my mood and knows what I need"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Wraps their arms around me when I’m sad', "Spends an afternoon with me, phone-free"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives a gift tied to something I love', "Lifts a burden when I’m struggling"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Quietly takes care of a task I hate', "Cooks a warm meal after a hard day"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Suggests visiting a new place together', "Makes a meal with my favorite ingredients"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Remembers all the little things I love', "Tells me I made a difference by being there"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Focuses on me during our quiet time', "Gives a custom gift that feels unique"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Thanks me for something small I did', "Gifts me something deeply meaningful"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gets what I’m thinking without words', "Rests their hand on my shoulder"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Fixes something I’ve been avoiding', "Plans a surprise trip just for us"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Cooks dinner when I’m stressed', "Hugs me after a stressful call"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me something I’ll treasure forever', "Holds my hand when we cross the street"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Cheers me on during something boring', "Hands me a blanket before I even ask"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Hugs me when I bring takeout', "Brings my favorite drink without asking"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Sends a sweet message to make me smile', "Goes out of the way to do something thoughtful"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me a gift with personal significance', "Cooks dinner with me and enjoys it together"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me a comforting touch when I’m upset', "Anticipates what I need and does it"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me a sweet hug after shopping', "Has my favorite food waiting to celebrate"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me something that reminds me of us', "Handles an errand I’ve been dreading"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives a carefully thought-out gift', "Understands me during silent moments"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Hands me the snack I’ve been craving', "Surprises me with a hug during laundry"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Tells me I’m amazing after a tough day', "Surprises me with a thoughtful gift"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Cuddles with me during Netflix', "Cooks a dish that feels like home"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Serves me a meal when I’m exhausted', "Remembers exactly how I like my coffee"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Leaves a cute note to brighten my day', "Senses when I need cheering up"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Touches my back while I’m cooking', "Laughs with me while watching silly videos"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gets excited about our weekend plans', "Surprises me with coffee to brighten my day"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Takes over when I’m overwhelmed', "Lets me lean on them during a movie"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Helps with a project I’m stuck on', "Senses when I’m reflective and listens"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Watches our favorite shows together', "Notices all my hard work this week"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Surprises me with breakfast in bed', "Says “You’ve got this!” during stress"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Makes a moment feel special with a thoughtful gift', "Jumps in to help with something I’m avoiding"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Playfully nudges me when I spill coffee', "Whips up something special to celebrate"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Tells me I’m great when I’m stuck', "Understands me with just a glance"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives a small gift that shows I’m valued', "Plans a dinner with all the foods I love"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Gives me something I’ve been hoping for', "Thanks me for tidying up a little"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Says something sweet after a hard meeting', "Steps in when I can’t manage alone"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Prepares a dish with extra care and love', "Spends a cozy night in, just us"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Praises me for a quick errand', "Does something small to ease my day"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Says something loving after a bad day', "Makes me feel known just by being there"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Completes a task to make me smile', "Makes a meal just to cheer me up"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Tries a new hobby or activity with me', "Gives me a gift to celebrate a special occasion"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Knows what I need before I do', "Squeezes my hand while walking"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Serves something warm when I’m feeling down', "Surprises me with a lunch date"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Snuggles close on a quiet night', "Takes me on a weekend adventure"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Turns a simple walk into a special moment', "Says something nice after a little fight"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Surprises me with a homemade treat', "Offers help before I ask"],
    },
    {
        question: 'I feel most loved when my partner',
        answers: ['Surprises me with a gift that shows they know me', "Knows when I need quiet time"],
    },            
    {
        question: 'Select your gender',
        answers: ['Female', 'Male', 'Prefer not to say'],
    },
]