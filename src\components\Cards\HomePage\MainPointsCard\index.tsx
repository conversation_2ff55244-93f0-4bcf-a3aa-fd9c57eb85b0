import { memo } from 'react';
import Image from 'next/image';

const MainPointsCard = ({ order, img, title, text }: { order: number; img: string; title: string; text: string }) => (
  <div className='flex-initial gap-5 relative'>
    <Image
      style={{}}
      src={img}
      alt={`Illustration for main points about IQ tests.`}
      width={412}
      height={412}
      priority
    />
    <h4 style={{ marginTop: 30 }}>{title}</h4>
    <p style={{ maxWidth: 412, marginTop: 10 }}>{text}</p>
    {order === 0 ? (
      <Image
        style={{ position: 'absolute', top: '-35px', left: '-45px' }}
        src={`/home/<USER>/wave.svg`}
        alt={`Small wave as decoration for main points.`}
        width={177}
        height={154}
      />
    ) : (
      ''
    )}
  </div>
);

export default memo(MainPointsCard);
