'use client';

import { useContext, useEffect, useState } from "react";
import { useRouter } from '@/lib/i18n/navigation';
import { sendGTMEvent } from "@next/third-parties/google";
import SessionContext from "@/store/SessionContext";
import UiContext from "@/store/UiContext";
import { Prices } from "@/app/prices";
import useGetMerchantData from "@/hooks/useGetMerchantData";
import { isGTMInitialized } from "@/utils/isGtmInitialized";
import { getClickId } from "@/utils/getClickId";
import PaymentStripe from "../PaymentDetails";
import PaymentSolidgate from "../PaymentDetailsSolidgate";

interface PaymentGatewayProps {
  paymentSystem: string;
  isTrial: boolean;
}

function PaymentGateway({ paymentSystem, isTrial }: PaymentGatewayProps) {
  const router = useRouter();
  const clickId = getClickId();
  const { prices, updateMerchantData } = useContext(SessionContext);
  const { time } = useContext(UiContext);
  const { getMerchantData } = useGetMerchantData();
  const [clientSecret, setClientSecret] = useState('');

  /**
   * Tracks the payment process in Google Tag Manager.
   * 
   * @param prices 
   * @returns 
   */
  function trackGTMEvent(prices: Prices) {
    const query = new URLSearchParams(window?.location?.search);
    const status = query.get('status');
      
    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on Checkout page: Event not sent');
      return;
    }
  
    if (status === 'canceled') {
      sendGTMEvent({ event: 'payment_canceled' });
    } else {
      sendGTMEvent({
        event: 'begin_checkout',
        ecommerce: {
          currency: prices.currency.toUpperCase(),
          items: [
            {
              item_name: 'IQ test',
              item_id: 'iqtest',
              price: prices.oneTime.amount,
              quantity: 1,
            },
          ],
        },
      });
    }
  }

  useEffect(() => {
    const resolveMerchantData = async () => {
      const fetchedMerchantData = await getMerchantData({ clickId, time, isTrial });
      updateMerchantData(fetchedMerchantData);

      if (paymentSystem !== 'solidgate') {
        const cs = fetchedMerchantData;
        if (!cs) {
          router.push('/');
          return;
        }

        setClientSecret(cs!);
      }

      trackGTMEvent(prices);
    };

    resolveMerchantData();
  }, [paymentSystem, clickId, time, isTrial, prices]);

  if (paymentSystem === 'solidgate') {
    return <PaymentSolidgate />;
  }

  return <PaymentStripe clientSecret={clientSecret} />;
}

export default PaymentGateway;
