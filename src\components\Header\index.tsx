'use client';
import { useContext } from 'react';
import { menuItems } from '@/app/config';
import Brand from '@/components/Brand';
import CtaButton from '@/components/Buttons/CtaButton';
import HamburgerButton from '@/components/Buttons/HamburgerButton';
import MenuItem from '@/components/Menu/MenuItem';
import MobileMenu from '@/components/MobileMenu';
import { usePathname } from '@/lib/i18n/navigation';
import { UserContext } from '@/store/UserContext';

const Header = () => {
  const { user } = useContext(UserContext);
  const activePath = usePathname();

  if (
    [
      '/form',
      '/calculation',
      '/checkout',
      '/mobile-checkout',
      '/upsell',
      '/questions',
    ].includes(activePath)
  )
    return null;

  return (
    <div className="header relative z-50 w-full py-3 flex justify-center px-[5%] md:px-[5.69444%] bg-transparent">
      <div className="w-full flex gap-5 xxs:gap-0 items-center justify-between 2xl:max-w-[calc(1440px)] m-auto">
        <Brand {...{ classes: '!justify-start' }} />
        <nav className={`header-links mt-[15px] mb-[4px] hidden lg:flex gap-10 py-2 items-center`}>
          {menuItems
            .filter(x =>
              !(activePath == '/results' && x.id == 'pricing') && !user ? !x.memberOnly : !x.hideIfLoggedIn
            )
            .map((item, _i) => (
              <MenuItem key={item.id} {...{ item }} />
            ))}
        </nav>
        <div className="flex">
          <div className="md:block">
            <CtaButton {...{ type: 'secondary' }} />
          </div>
          <div className="flex lg:hidden order-2">
            <HamburgerButton />
          </div>
          <MobileMenu />
        </div>
      </div>
    </div>
  );
};

export default Header;
