import Image from 'next/image';
import UpsalePath from '@/components/UpsaleFlow/UpselPath';
import { useState } from 'react';
import UpsaleCard from '../UpsaleCard';

export default function UpsaleFive({ handleNext }) {
  const [choose, setChoose] = useState(1);
  return (
    <div>
      <div className="container mx-auto">
        <UpsalePath section={5} />
        <div className="text-center text-[52px] text-[#191919] pt-[40px] md:pt-[60px]">
          <h1 className="text-[24px] md:text-[36px] pb-1">Go Offline – Stay Ahead</h1>
          <p className="text-[13px] md:text-[14px]">
            No Wi-Fi? No problem! Get instant access to all site features <br className="hidden md:block" />
            without the need for an internet connection
          </p>
          <h3 className="pt-1 md:pt-4 text-[20px] md:text-[24px]">What you get</h3>
        </div>
        <div className="pb-2 md:pb-10 pt-0 md:pt-4">
          <UpsaleCard
            icon={
              <svg
                className="mx-auto"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M27.0713 4.92996C25.4829 3.33935 23.4049 2.3303 21.1726 2.06565C18.9403 1.80101 16.684 2.29619 14.7677 3.47131C12.8514 4.64642 11.3869 6.43292 10.6106 8.54251C9.83424 10.6521 9.79133 12.9617 10.4888 15.0987L3.58626 22.0012C3.39973 22.1863 3.25185 22.4065 3.15121 22.6492C3.05057 22.8919 2.99917 23.1522 3.00001 23.415V27.0012C3.00001 27.5316 3.21072 28.0403 3.5858 28.4154C3.96087 28.7905 4.46958 29.0012 5.00001 29.0012H9.00001C9.26523 29.0012 9.51958 28.8959 9.70712 28.7083C9.89465 28.5208 10 28.2664 10 28.0012V26.0012H12C12.2652 26.0012 12.5196 25.8959 12.7071 25.7083C12.8947 25.5208 13 25.2664 13 25.0012V23.0012H15C15.1314 23.0013 15.2615 22.9755 15.3829 22.9253C15.5043 22.8752 15.6146 22.8015 15.7075 22.7087L16.9025 21.5125C17.9028 21.8378 18.9482 22.0028 20 22.0012H20.0125C21.9893 21.9988 23.9211 21.4106 25.5637 20.3108C27.2063 19.2111 28.4862 17.6491 29.2415 15.8223C29.9969 13.9955 30.1939 11.9858 29.8076 10.0471C29.4213 8.10845 28.4691 6.32773 27.0713 4.92996ZM28 12.2637C27.8638 16.525 24.2813 19.9962 20.0138 20.0012H20C18.9877 20.0029 17.9844 19.8116 17.0438 19.4375C16.8598 19.3576 16.656 19.3349 16.4589 19.3724C16.2619 19.4098 16.0806 19.5057 15.9388 19.6475L14.5863 21.0012H12C11.7348 21.0012 11.4804 21.1066 11.2929 21.2941C11.1054 21.4816 11 21.736 11 22.0012V24.0012H9.00001C8.73479 24.0012 8.48044 24.1066 8.2929 24.2941C8.10537 24.4816 8.00001 24.736 8.00001 25.0012V27.0012H5.00001V23.415L12.3538 16.0625C12.4955 15.9206 12.5914 15.7393 12.6288 15.5423C12.6663 15.3452 12.6436 15.1415 12.5638 14.9575C12.1884 14.0137 11.9971 13.0069 12 11.9912C12 7.72371 15.4763 4.14121 19.7375 4.00496C20.8321 3.96843 21.9225 4.15704 22.9413 4.5591C23.96 4.96117 24.8853 5.56812 25.6599 6.34237C26.4345 7.11662 27.0419 8.04167 27.4444 9.06021C27.8469 10.0788 28.036 11.1691 28 12.2637ZM24 9.50121C24 9.79788 23.912 10.0879 23.7472 10.3346C23.5824 10.5812 23.3481 10.7735 23.074 10.887C22.7999 11.0006 22.4983 11.0303 22.2074 10.9724C21.9164 10.9145 21.6491 10.7716 21.4394 10.5619C21.2296 10.3521 21.0867 10.0848 21.0288 9.79385C20.971 9.50287 21.0007 9.20127 21.1142 8.92718C21.2277 8.6531 21.42 8.41883 21.6667 8.254C21.9133 8.08918 22.2033 8.00121 22.5 8.00121C22.8978 8.00121 23.2794 8.15925 23.5607 8.44055C23.842 8.72185 24 9.10338 24 9.50121Z"
                  class="fill-primary"
                />
              </svg>
            }
            header="Access Anytime, Anywhere"
            text="Use all site features offline for uninterrupted training"
          />
          <UpsaleCard
            icon={
              <svg
                className="mx-auto"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M29.9656 15.4514C29.9625 15.4233 29.9609 15.3967 29.9562 15.3686C29.9328 15.1983 29.9 15.0295 29.8593 14.8623C29.8515 14.8311 29.8437 14.7999 29.8343 14.7686V14.767C29.7906 14.6014 29.7375 14.4389 29.675 14.278C29.6656 14.2545 29.6562 14.2326 29.6468 14.2108C29.5828 14.0498 29.5093 13.8936 29.4281 13.7404C29.4234 13.7326 29.4203 13.7248 29.4171 13.717V13.7186C29.3328 13.5639 29.239 13.4154 29.139 13.2717C29.1218 13.2467 29.1062 13.2233 29.089 13.1983C28.989 13.0623 28.8828 12.9295 28.7703 12.8045C28.7484 12.7795 28.7265 12.7561 28.7031 12.7326L28.7046 12.7311C28.5874 12.6045 28.464 12.4842 28.3343 12.3717C28.3171 12.3576 28.2999 12.3436 28.2828 12.3295V12.3279C28.1531 12.2186 28.0171 12.1154 27.8765 12.0201C27.8671 12.0154 27.8578 12.0061 27.8484 11.9983C28.1859 10.6858 27.9546 9.29201 27.2093 8.15917C26.464 7.02793 25.2765 6.26229 23.9374 6.05449C23.6359 4.29669 22.3046 2.89513 20.564 2.50449C18.8234 2.11543 17.0202 2.81699 16 4.27793C14.9781 2.81701 13.175 2.11545 11.4344 2.50605C9.69532 2.89668 8.36252 4.29825 8.06252 6.05605C6.725 6.26543 5.53596 7.02949 4.7922 8.16229C4.04688 9.29353 3.81408 10.6857 4.15156 11.9982C4.14062 12.0045 4.13281 12.0138 4.12187 12.0201C3.98124 12.1154 3.84687 12.2185 3.71563 12.3279C3.69844 12.342 3.67969 12.356 3.6625 12.3717C3.53438 12.4842 3.41094 12.6029 3.29532 12.7295C3.27188 12.7545 3.25 12.7779 3.22656 12.8029C3.11406 12.9279 3.00938 13.0607 2.90938 13.1967C2.89219 13.2217 2.875 13.2467 2.85781 13.2717C2.75781 13.4138 2.66562 13.5623 2.58125 13.7154C2.57656 13.7232 2.57344 13.7326 2.56875 13.7404C2.4875 13.892 2.41562 14.0482 2.35156 14.2076C2.34219 14.231 2.33125 14.2545 2.32187 14.2779C2.26094 14.4373 2.20781 14.5998 2.16406 14.7654C2.15625 14.7967 2.14687 14.8279 2.13906 14.8608H2.14062C2.09844 15.0279 2.06562 15.1967 2.04219 15.367C2.03906 15.3936 2.03594 15.4217 2.03281 15.4498H2.03438C2.0125 15.6311 2 15.8154 2 15.9982C2.00156 16.1857 2.0125 16.3717 2.03594 16.5576C2.04062 16.6014 2.05 16.6451 2.05625 16.6904C2.07812 16.8373 2.10469 16.981 2.13906 17.1232C2.14844 17.1638 2.15937 17.2013 2.16875 17.2388C2.21094 17.3982 2.26094 17.5529 2.32031 17.706L2.33906 17.7576C2.47969 18.1139 2.6625 18.4514 2.88438 18.7638L2.90001 18.7842V18.7857C3.00157 18.9279 3.11094 19.0638 3.22813 19.1951C3.24688 19.2154 3.26563 19.2373 3.28594 19.2576C3.40313 19.3841 3.52657 19.5045 3.6547 19.617C3.66563 19.6263 3.67657 19.6373 3.68751 19.6466H3.68907C3.83595 19.7732 3.99064 19.8904 4.15155 19.9982C3.81405 21.3107 4.0453 22.7044 4.79063 23.8357C5.53595 24.9685 6.72343 25.7326 8.06251 25.942C8.36251 27.6998 9.69531 29.1013 11.436 29.4904C13.1766 29.881 14.9797 29.1795 16 27.7185C17.0218 29.1794 18.825 29.881 20.5656 29.4904C22.3046 29.0998 23.6374 27.6982 23.9374 25.9404C25.275 25.731 26.464 24.967 27.2078 23.8357C27.9531 22.7029 28.1859 21.3107 27.8484 19.9982C28.0093 19.8904 28.164 19.7732 28.3125 19.6466C28.3234 19.6373 28.3343 19.6263 28.3453 19.617C28.475 19.5029 28.5968 19.3826 28.714 19.256C28.7343 19.2357 28.7547 19.2154 28.7718 19.1935C28.889 19.0638 28.9984 18.9263 29.1015 18.7842L29.1172 18.7638H29.1156C29.3375 18.4513 29.5203 18.1138 29.6609 17.7576L29.6797 17.706V17.7076C29.739 17.5545 29.789 17.3982 29.8312 17.2404C29.8422 17.1998 29.8515 17.1623 29.8609 17.1232C29.8953 16.981 29.9219 16.8373 29.9437 16.692C29.95 16.6482 29.9594 16.6045 29.964 16.5592C29.9875 16.3732 29.9984 16.1857 30 15.9982C29.9984 15.8154 29.9875 15.6327 29.9656 15.4514ZM15.2 21.8078C13.6984 20.5656 11.5578 20.4594 9.94198 21.5484C9.76229 21.6656 9.63729 21.85 9.5951 22.0593C9.55135 22.2703 9.5951 22.489 9.71541 22.6656C9.83573 22.8437 10.0217 22.9671 10.2326 23.0062C10.4436 23.0453 10.6607 22.9984 10.8373 22.875C11.9029 22.1578 13.3201 22.2578 14.2748 23.1187C15.2295 23.9796 15.4748 25.3796 14.8717 26.514C14.267 27.6484 12.9686 28.225 11.7217 27.914C10.4748 27.6031 9.59982 26.4828 9.59982 25.1984V24.3984H8.79981C7.85449 24.4031 6.95605 23.9875 6.34669 23.264C5.73889 22.5406 5.48261 21.5844 5.64981 20.6531C7.53573 21.1297 9.52325 20.4094 10.6654 18.8344C11.4029 19.089 12.1967 19.1406 12.9623 18.9797C13.3951 18.8906 13.6732 18.4672 13.5842 18.0343C13.4936 17.6015 13.0701 17.3234 12.6373 17.4125C11.6248 17.6218 10.5779 17.2547 9.91697 16.4578C9.25605 15.6609 9.08885 14.5656 9.48105 13.6078C9.64981 13.1984 9.45293 12.7312 9.04353 12.564C8.63574 12.3953 8.16697 12.5922 7.99977 13.0015C7.27321 14.775 7.7779 16.814 9.24666 18.0435C8.35446 19.1248 6.86073 19.4935 5.56697 18.9513L5.47791 18.9107L5.44979 18.8967V18.8982C5.17479 18.7701 4.92167 18.6045 4.69355 18.406L4.68105 18.3951V18.3935C4.34667 18.0982 4.07793 17.7357 3.89041 17.3294L3.88884 17.3185C3.82634 17.1826 3.77478 17.0435 3.73259 16.9013V16.8951C3.69509 16.7669 3.66697 16.6357 3.64509 16.5044C3.64041 16.4747 3.63572 16.4451 3.63103 16.4154L3.63259 16.4138C3.61228 16.2763 3.60135 16.1372 3.59978 15.9982C3.60447 14.7528 4.3279 13.6216 5.45758 13.0966L6.16226 12.77L5.85601 12.056H5.85758C5.47945 11.1716 5.51851 10.1653 5.96539 9.31376C6.41071 8.4622 7.21695 7.85752 8.15759 7.66376C9.09979 7.47157 10.0779 7.7122 10.8232 8.32C11.567 8.92624 11.9998 9.8372 11.9998 10.7981C11.9998 11.2403 12.3576 11.5981 12.7998 11.5981C13.242 11.5981 13.5998 11.2403 13.5998 10.7981C13.5967 8.495 11.9576 6.51692 9.69511 6.08572C10.0592 4.72948 11.3685 3.85292 12.7592 4.03416C14.1514 4.21385 15.1935 5.39508 15.1998 6.79824L15.2 21.8078ZM28.3704 16.417C28.3657 16.4467 28.361 16.4764 28.3563 16.5061C28.336 16.6373 28.3063 16.7686 28.2704 16.8967V16.9029C28.2266 17.0451 28.1751 17.1842 28.1126 17.3201L28.1079 17.3295V17.328C27.9219 17.7342 27.6516 18.0967 27.3172 18.392L27.3047 18.403V18.4045C27.0782 18.603 26.8235 18.7686 26.5501 18.8967L26.5204 18.9108L26.4313 18.9514L26.4297 18.9499C25.1375 19.4921 23.6438 19.1233 22.7501 18.0421C24.2204 16.8139 24.7251 14.7749 24.0001 13.0013C23.8266 12.6028 23.3657 12.4169 22.9641 12.5809C22.561 12.7466 22.3641 13.2013 22.5204 13.6075C22.9126 14.5653 22.7454 15.6606 22.0845 16.4575C21.4235 17.2544 20.3782 17.6216 19.3641 17.4122C18.9313 17.3231 18.5079 17.6012 18.4188 18.0341C18.3298 18.4669 18.6079 18.8903 19.0407 18.9794C19.8048 19.1403 20.5985 19.0888 21.3376 18.8341C22.4782 20.4091 24.4657 21.1294 26.3532 20.6528C26.5188 21.5841 26.2626 22.5403 25.6547 23.2637C25.0454 23.9887 24.1454 24.4028 23.2001 24.3981H22.4001V25.1981C22.4001 26.4825 21.5251 27.6028 20.2782 27.9137C19.0313 28.2247 17.7329 27.6481 17.1282 26.5137C16.5251 25.3794 16.7704 23.9794 17.7251 23.1184C18.6797 22.2575 20.0969 22.1575 21.1626 22.8747C21.3392 22.9981 21.5563 23.045 21.7673 23.0059C21.9782 22.9669 22.1641 22.8434 22.2845 22.6653C22.4048 22.4887 22.4485 22.27 22.4048 22.0591C22.3626 21.8497 22.2376 21.6653 22.0579 21.5481C20.4423 20.4591 18.3017 20.5653 16.7999 21.8075V6.79831C16.8062 5.39519 17.8483 4.21395 19.2405 4.03423C20.6312 3.85299 21.9405 4.72955 22.3046 6.08579C20.0421 6.51703 18.4031 8.49515 18.3999 10.7982C18.3999 11.2404 18.7578 11.5982 19.1999 11.5982C19.6421 11.5982 19.9999 11.2404 19.9999 10.7982C19.9999 9.83727 20.4327 8.92631 21.1765 8.32007C21.9218 7.71227 22.8999 7.47163 23.8421 7.66383C24.7828 7.85758 25.589 8.46227 26.0343 9.31383C26.4812 10.1654 26.5203 11.1716 26.1422 12.056L25.8359 12.7701L26.5406 13.0967H26.5422C27.6718 13.6217 28.3953 14.7529 28.3999 15.9982C28.3984 16.1389 28.3891 16.2779 28.3704 16.417Z"
                  class="fill-primary"
                />
              </svg>
            }
            header="Exclusive IQ Training Modules"
            text="Continue sharpening your mind without internet"
          />
          <UpsaleCard
            icon={
              <svg
                className="mx-auto stroke-primary"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M20.7867 19.16C21.0643 20.3348 21.0725 21.5571 20.8106 22.7355C20.5488 23.9139 20.0236 25.0177 19.2745 25.9643C18.5255 26.9109 17.5719 27.6757 16.4853 28.2015C15.3986 28.7272 14.2071 29.0002 13 29V22.6M20.7867 19.16C23.3332 17.305 25.4048 14.874 26.8322 12.0654C28.2597 9.25689 29.0025 6.15049 29 3.00001C25.8497 2.99773 22.7436 3.74065 19.9353 5.16809C17.127 6.59553 14.6962 8.66701 12.8413 11.2133M20.7867 19.16C18.4692 20.855 15.8138 22.0286 13 22.6M13 22.6C12.8627 22.628 12.724 22.6547 12.5853 22.68C11.3757 21.7208 10.2805 20.6256 9.32133 19.416C9.34652 19.2771 9.37186 19.1384 9.4 19M12.8413 11.2133C11.6665 10.9355 10.444 10.9271 9.26542 11.1889C8.08688 11.4506 6.98286 11.9757 6.0361 12.7248C5.08934 13.4739 4.3244 14.4276 3.7986 15.5143C3.2728 16.6011 2.99978 17.7927 3 19H9.4M12.8413 11.2133C11.1465 13.5304 9.97167 16.1867 9.4 19M6.41467 22.1867C5.53757 22.8389 4.85564 23.7186 4.4427 24.7307C4.02976 25.7427 3.90157 26.8484 4.072 27.928C5.15177 28.0983 6.25755 27.9699 7.26957 27.5567C8.28159 27.1435 9.16127 26.4613 9.81333 25.584M22 12C22 12.5304 21.7893 13.0391 21.4142 13.4142C21.0391 13.7893 20.5304 14 20 14C19.4696 14 18.9609 13.7893 18.5858 13.4142C18.2107 13.0391 18 12.5304 18 12C18 11.4696 18.2107 10.9609 18.5858 10.5858C18.9609 10.2107 19.4696 10 20 10C20.5304 10 21.0391 10.2107 21.4142 10.5858C21.7893 10.9609 22 11.4696 22 12Z"
                  stroke-width="1.75"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            }
            header="Keep Improving"
            text="Stay on track and keep progressing, even offline"
          />
          <UpsaleCard
            icon={
              <svg
                className="mx-auto"
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M13.9373 6.57875C13.7025 6.3445 13.4124 6.17318 13.0938 6.0806C12.7753 5.98802 12.4386 5.97716 12.1147 6.04903C11.7909 6.12089 11.4904 6.27317 11.2409 6.49181C10.9914 6.71044 10.801 6.98839 10.6873 7.3L4.12607 25.3475C4.01829 25.6451 3.98347 25.9642 4.02454 26.2781C4.06562 26.5919 4.18139 26.8914 4.36213 27.1512C4.54288 27.4111 4.78332 27.6238 5.06328 27.7715C5.34324 27.9191 5.65455 27.9975 5.97107 28C6.20412 27.9984 6.43509 27.9561 6.65357 27.875L24.6998 21.3125C25.0115 21.1989 25.2896 21.0086 25.5084 20.7592C25.7271 20.5098 25.8796 20.2093 25.9516 19.8855C26.0235 19.5616 26.0128 19.2248 25.9203 18.9062C25.8278 18.5876 25.6565 18.2975 25.4223 18.0625L13.9373 6.57875ZM12.8961 23.48L8.52107 19.105L10.1661 14.5787L17.4223 21.835L12.8961 23.48ZM6.02107 25.98L7.77107 21.1787L10.8273 24.235L6.02107 25.98ZM19.5011 21.08L10.9211 12.5L12.5461 8.01625L23.9748 19.445L19.5011 21.08ZM20.0011 9C20.0199 8.32396 20.1835 7.65983 20.4811 7.0525C21.1436 5.72875 22.3936 5 24.0011 5C24.8386 5 25.3761 4.71375 25.7073 4.09875C25.8817 3.75468 25.9818 3.37777 26.0011 2.9925C26.0021 2.72728 26.1084 2.47332 26.2966 2.28649C26.4849 2.09966 26.7396 1.99526 27.0048 1.99625C27.27 1.99724 27.524 2.10356 27.7108 2.29179C27.8977 2.48003 28.0021 2.73478 28.0011 3C28.0011 4.6075 26.9361 7 24.0011 7C23.1636 7 22.6261 7.28625 22.2948 7.90125C22.1204 8.24532 22.0203 8.62223 22.0011 9.0075C22.0006 9.13882 21.9742 9.26876 21.9235 9.3899C21.8728 9.51103 21.7987 9.621 21.7055 9.71351C21.6123 9.80602 21.5018 9.87926 21.3803 9.92906C21.2588 9.97886 21.1286 10.0042 20.9973 10.0037C20.866 10.0033 20.7361 9.9769 20.6149 9.92619C20.4938 9.87548 20.3838 9.80141 20.2913 9.70821C20.1988 9.615 20.1256 9.50448 20.0758 9.38297C20.026 9.26145 20.0006 9.13132 20.0011 9ZM17.0011 5V2C17.0011 1.73478 17.1064 1.48043 17.294 1.29289C17.4815 1.10536 17.7359 1 18.0011 1C18.2663 1 18.5206 1.10536 18.7082 1.29289C18.8957 1.48043 19.0011 1.73478 19.0011 2V5C19.0011 5.26522 18.8957 5.51957 18.7082 5.70711C18.5206 5.89464 18.2663 6 18.0011 6C17.7359 6 17.4815 5.89464 17.294 5.70711C17.1064 5.51957 17.0011 5.26522 17.0011 5ZM29.7086 15.2925C29.8014 15.3854 29.875 15.4957 29.9252 15.6171C29.9754 15.7384 30.0012 15.8685 30.0012 15.9998C30.0011 16.1312 29.9752 16.2612 29.9249 16.3825C29.8746 16.5038 29.8009 16.614 29.7079 16.7069C29.615 16.7997 29.5047 16.8733 29.3834 16.9235C29.262 16.9737 29.132 16.9995 29.0006 16.9995C28.8693 16.9994 28.7393 16.9735 28.6179 16.9232C28.4966 16.8729 28.3864 16.7992 28.2936 16.7062L26.2936 14.7063C26.1059 14.5186 26.0005 14.2641 26.0005 13.9987C26.0005 13.7334 26.1059 13.4789 26.2936 13.2913C26.4812 13.1036 26.7357 12.9982 27.0011 12.9982C27.2664 12.9982 27.5209 13.1036 27.7086 13.2913L29.7086 15.2925ZM30.3173 9.94875L27.3173 10.9487C27.0657 11.0326 26.7911 11.0131 26.5538 10.8945C26.3166 10.7759 26.1362 10.5679 26.0523 10.3162C25.9684 10.0646 25.988 9.78999 26.1066 9.55276C26.2252 9.31552 26.4332 9.13513 26.6848 9.05125L29.6848 8.05125C29.9364 7.96738 30.2111 7.98689 30.4483 8.10551C30.6855 8.22413 30.8659 8.43213 30.9498 8.68375C31.0337 8.93537 31.0142 9.21001 30.8956 9.44724C30.7769 9.68448 30.5689 9.86487 30.3173 9.94875Z"
                  class="fill-primary"
                />
              </svg>
            }
            header="One-Time Payment of Only $3.99"
            text="Enjoy lifetime offline access with a single payment"
          />
        </div>
        <div className="flex border-t-2 justify-between">
          <button
            onClick={() => {
              handleNext();
            }}
            className="md:px-8 w-[32%] mx-[1%] md:w-auto py-3 bg-[#E8EDF8] text-black rounded-md md:my-6">
            Skip
          </button>
          <button
            onClick={() => {
              handleNext();
            }}
            className="text-white w-[66%] mx-[1%] md:w-[300px] md:px-8 py-3 bg-primary rounded-md md:my-6">
            Return to Toolkits
          </button>
        </div>
      </div>
    </div>
  );
}
