import { useMemo } from 'react';
import TestimonialCard from '@/components/Cards/CheckoutPage/TestimonialCard';

const TestimonialSection = () => {
  const testimonials = useMemo(
    () => [
      {
        title: 'Valuable insights',
        text: 'The IQ test gave me valuable insights. Highly beneficial for personal growth!',
      },
      {
        title: 'Must-try',
        text: 'Incredibly valuable insights gained from the IQ test. A must-try!',
      },
      {
        title: 'Impressive Revelations',
        text: 'I was highly impressed with the IQ test — very detailed and helpful!',
      },
      {
        title: 'Growth Booster',
        text: 'Extremely beneficial IQ test results for self-discovery and growth.',
      },
      {
        title: 'Mind Unveiled',
        text: 'This test opened my eyes to cognitive strengths. A truly enlightening experience!',
      },
      {
        title: 'Cognitive Mastery',
        text: 'The IQ test’s depth of analysis exceeded expectations. A tool for cognitive mastery.',
      },
    ],
    [],
  );

  return (
    <div className='md:mt-10 lg:mt-0'>
      <h4 className='text-center md:text-left mb-4 md:mb-6'>What our customers are saying</h4>
      <ul className='bg-grey-bg flex flex-wrap p-6 mx-4 md:mx-0'>
        {/**gap:5 */}
        {testimonials.map((testimonial, index) => (
          <TestimonialCard key={index} {...{ testimonial, index: index + 1 }} />
        ))}
      </ul>
    </div>
  );
};

export default TestimonialSection;
