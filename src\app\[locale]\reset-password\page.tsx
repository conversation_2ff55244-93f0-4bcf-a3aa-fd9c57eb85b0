'use client';

import '@/sass/form.scss';
import { useEffect, useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { Loader2 } from 'lucide-react';
import { z } from 'zod';
import { Link } from '@/lib/i18n/navigation';
import { functions } from '@/utils/firebase';

const passwordStrength = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /[0-9]/.test(password);

  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
};

export default function ResetPassword() {
  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirmed, setNewPasswordConfirmed] = useState('');
  const [message, setMessage] = useState('');
  const [processing, setProcessing] = useState(false);
  const [uid, setUid] = useState('');
  const [token, setToken] = useState('');
  const [success, setSuccess] = useState(false);

  async function resetPassword() {
    setProcessing(true);
    if (newPassword !== newPasswordConfirmed) {
      setMessage('Passwords do not match');
      setProcessing(false);
      return;
    }
    const isValidPassword = z.string().refine(passwordStrength).safeParse(newPassword).success;

    if (!isValidPassword) {
      setMessage('Password must be at least 8 characters long and include uppercase letters, lowercase letters, and numeric characters');
      setProcessing(false);
      return;
    }

    setMessage('');
    const sendEmail = httpsCallable(functions, 'resetPassword');
    const result: any = await sendEmail({ uid, token, password: newPassword });
    if (result.data.success)
        setSuccess(true);
    else 
        setMessage(result.data.message);

    setProcessing(false);
  }

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const token = query.get('token');
    const uid = query.get('uid');

    if (!token || !uid) {
        setMessage('Invalid reset link');
        return;
    }

    setUid(uid);
    setToken(token);
  }, []);

  if (message == 'Invalid reset link') {
    return <div className="flex flex-col justify-center items-center">Invalid reset link</div>;
  } else if (success) {
    return <div className="flex flex-col justify-center items-center gap-5 px-5">
        <h3 className='text-center'>Password reset successful</h3>
        <p className='text-center'>Your password has been reset successfully. You can now login with your new password.</p>
        <Link href="/login" className="primary rounded-lg mt-2 flex justify-center">Login</Link>
    </div>;
  }

  return (
    <div className="flex flex-col justify-center items-center">
      <h3>Reset password</h3>
      <div id="msform" className='flex flex-col items-center justify-center'>
        <input
          type="password"
          name="new_password"
          value={newPassword}
          onChange={e => setNewPassword(e.target.value)}
          placeholder="New password"
          required
        />
        <input
          type="password"
          name="new_password_confirmed"
          value={newPasswordConfirmed}
          onChange={e => setNewPasswordConfirmed(e.target.value)}
          placeholder="New password confirmed"
          required
        />
        {message != '' && <span className="block mb-2 text-left mt-2 pl-[10px]">{message}</span>}
        <button disabled={processing} onClick={resetPassword} className="primary rounded-lg mt-2 flex justify-center">
          {processing ? <Loader2 className="h-4 w-4 animate-spin m-[5px] mr-2" /> : ''}
          Set new password
        </button>
      </div>
    </div>
  );
}
