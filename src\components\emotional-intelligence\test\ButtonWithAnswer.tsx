'use client';

import Image from 'next/image';
import React, { useState } from 'react';

interface ButtonWithAnswerProps {
  label: string;
  src: string;
  alt: string;
  onClick: () => void;
  isClicked: boolean;
}

// Helper component to render the images
const AnswerImage: React.FC<{
  src: string;
  alt: string;
  isPressed: boolean; // Track whether the button is pressed
  isClicked: boolean;
  label: string;
}> = ({ src, alt, isPressed, isClicked, label }) => {
  const scaleClass = label === 'Disagree' || label === 'Strongly Disagree' ? 'scale-y-[-1]' : '';
  // Set opacity to 100 when button is pressed or clicked, else 40
  const opacityClass = isPressed || isClicked ? 'opacity-100' : 'opacity-40';

  return (
    <Image
      src={src}
      alt={alt}
      width="28"
      height="28"
      className={`w-[24px] md:w-[28px] h-auto my-auto ${opacityClass} ${scaleClass}`}
    />
  );
};

const ButtonWithAnswer: React.FC<ButtonWithAnswerProps> = ({ label, src, alt, onClick, isClicked }) => {
  const [isPressed, setIsPressed] = useState<boolean>(false); // State to track button press

  // Handle mouse down and up events to track button press
  const handleMouseDown = () => {
    setIsPressed(true);
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  // Handle mouse leave to cancel the pressed state
  const handleMouseLeave = () => {
    setIsPressed(false);
  };

  // Button class names with "active" state styling
  const buttonClassNames = `flex flex-row w-full h-[56px] md:h-[64px] rounded-[10px] px-[20px] py-[18px] text-left text-[#0C0113] md:text-[#0E2432] font-ppmori text-[16px] md:text-lg font-normal leading-6 shadow-[0px_2px_14px_0px_#4100511A] ${
    isClicked || isPressed ? 'border bg-[#8C36D014] border-[#8C36D0]' : ''
  }`;

  return (
    <button
      onClick={onClick}
      onMouseDown={handleMouseDown} // Detect when button is pressed
      onMouseUp={handleMouseUp} // Detect when button is released
      onMouseLeave={handleMouseLeave} // Cancel active state when cursor leaves the button
      className={buttonClassNames}>
      <span className="my-auto">{label}</span>
      <div className="flex flex-row gap-1 ml-auto">
        <AnswerImage src={src} alt={alt} isPressed={isPressed} isClicked={isClicked} label={label} />
        {(label === 'Strongly Agree' || label === 'Strongly Disagree') && (
          <AnswerImage src={src} alt={alt} isPressed={isPressed} isClicked={isClicked} label={label} />
        )}
      </div>
    </button>
  );
};

export default ButtonWithAnswer;
