'use client';
import { loadStripe } from '@stripe/stripe-js';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

export default function PaymentResult() {
  const t = useTranslations('checkout');
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    async function checkStatus() {
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
      const clientSecret = new URLSearchParams(window?.location?.search).get('payment_intent_client_secret');

      if (!clientSecret) {
        return;
      }

      stripe!.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
        switch (paymentIntent?.status) {
          case 'succeeded':
            setMessage(t('payment_succeeded'));
            break;
          case 'processing':
            setMessage(t('payment_processing'));
            break;
          case 'requires_payment_method':
            setMessage(t('payment_failed'));
            break;
          default:
            setMessage(t('payment_error'));
            break;
        }
      });
    }
    checkStatus();
  }, [t]);

  return <>{message != null ? <div>{message}</div> : <h3 className="">{t('checking_payment_status')}</h3>}</>;
}
