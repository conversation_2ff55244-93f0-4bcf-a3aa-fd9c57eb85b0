'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

const PaymentMethods = () => {
  const t = useTranslations('footer');

  const paymentMethods = [
    { src: '/images/footer/card1.svg', alt: t('payment_methods.card1') },
    { src: '/images/footer/card2.svg', alt: t('payment_methods.card2') },
    { src: '/images/footer/card3.svg', alt: t('payment_methods.card3') },
    { src: '/images/footer/card4.svg', alt: t('payment_methods.card4') },
    { src: '/images/footer/card5.svg', alt: t('payment_methods.card5') },
  ];

  return (
    <div className="flex flex-row gap-[6px]">
      {paymentMethods.map((method, index) => (
        <Image
          key={index}
          src={method.src}
          alt={method.alt}
          width={46}
          height={32}
          quality={100}
          priority
          className="rounded-[8px] bg-white"
        />
      ))}
    </div>
  );
};

export default PaymentMethods;
