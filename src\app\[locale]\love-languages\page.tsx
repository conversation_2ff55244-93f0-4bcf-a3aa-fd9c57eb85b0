'use client';

import Image from 'next/image';
import { useContext } from 'react';
import { Link } from '@/lib/i18n/navigation';
import Logo from '@/components/love-languages/Logo';
import IconWithDescription from '@/components/love-languages/IconWithDescription';
import BoxWithIcon from '@/components/love-languages/BoxWithIcon';
import LoginButton from '@/components/love-languages/LoginButton';
import Footer from '@/components/love-languages/GFooter';
import Account from '@/components/love-languages/Account';
import { UserContext } from '@/store/UserContext';

const LandingPage = () => {
  const { user } = useContext(UserContext);

  return (
    <div className="relative w-full overflow-x-hidden">
      <Image
        src="/images/love-languages/gradient-desktop.png"
        alt="Desktop Photos"
        width={1110}
        height={830}
        className="absolute left-[calc(50%-159px)] top-[0px] w-[1130px] h-auto hidden md:block z-10"
      />
      <div className="max-w-[1440px] mx-auto relative">
        <Image
          src="/images/love-languages/photos-desktop.png"
          alt="Desktop Photos"
          width={630}
          height={649}
          className="absolute right-[78.33px] top-[145.15px] w-[630px] h-auto md:h-auto hidden md:block z-10"
        />
      </div>
      <header className="max-w-[1440px] mx-auto flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px] z-20">
        <Logo />
        {user ? <Account /> : <LoginButton />}
      </header>
      <main className="max-w-[1440px] mx-auto">
        <div className="hero-section relative w-full m-0 p-0 md:pb-[75px]">
          {/* <div className="relative z-0">
          <Image
            src="/images/love-languages/gradient-desktop.svg"
            alt="Desktop Gradient"
            width={890}
            height={830}
            className="absolute right-[0px] top-[0px] w-[890px] h-auto md:h-auto hidden md:block z-0"
            priority
          />
          <Image
            src="/images/love-languages/photos-desktop.svg"
            alt="Desktop Photos"
            width={620.27}
            height={638.12}
            className="absolute right-[78.33px] top-[145.15px] w-[620.27px] h-auto md:h-auto hidden md:block z-10"
          />
          <Image
            src="/images/love-languages/effects-desktop.svg"
            alt="Desktop Effects"
            width={271.2}
            height={207}
            className="absolute right-[395px] top-[121px] w-[271.2] h-auto md:h-auto hidden md:block z-5"
          />
        </div>
        <div className="flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px]">
          <Logo />
          <LoginButton />
        </div> */}
          <div className="px-[16px] pt-[16px] md:pl-[82px] md:pt-[17px] md:max-w-[668px] relative z-20 mb-[24px] md:mb-[0px]">
            <div className="inline-flex bg-[#F2FAFF] px-[16px] py-[6px] rounded-[60px] mb-2">
              <label className="text-[#5DC4FF] font-raleway font-semibold text-[14px] leading-[20px] md:text-[16px] md:leading-[24px]">
                BEYOND <span className="font-sans">5</span> LOVE LANGUAGES
              </label>
            </div>
            <label className="block text-[#0E2432] font-raleway font-bold tracking-[-0.05em] text-[36px] leading-[40px] md:mb-[28px] md:text-[64px] md:leading-[74px] mb-[16px] z-20">
              Discover Your <br />
              True Love Language
            </label>
            <label className="block text-[#828E98] font-raleway text-[16px] leading-[22px] md:text-[18px] md:leading-[27px] font-medium md:mr-[50px] mb-[24px]">
              Unlock a deeper understanding of yourself and those you care about. Our Love Styles Test reveals how you
              connect and feel most loved, empowering you to build{' '}
              <span className="font-semibold text-[#0E2432]">stronger</span>, more{' '}
              <span className="font-semibold text-[#0E2432]">meaningful relationships</span>.
            </label>
            <div className="flex flex-col gap-[16px] mb-[40px]">
              <IconWithDescription source="/images/love-languages/heart.png" alt="Heart Icon">
                Understand the unique ways you express and experience love—it can{' '}
                <span className="font-semibold text-[#0E2432]">transform</span> your{' '}
                <span className="font-semibold text-[#0E2432]">relationships</span>
              </IconWithDescription>
              <IconWithDescription source="/images/love-languages/connection.png" alt="Connection Icon">
                <span className="font-semibold text-[#0E2432]">Strengthen</span> your{' '}
                <span className="font-semibold text-[#0E2432]">connections</span>
                —with your partner, friends, family, and even yourself{' '}
              </IconWithDescription>
              <IconWithDescription source="/images/love-languages/growth.png" alt="Growth Icon">
                Embrace personal <span className="font-semibold text-[#0E2432]">growth</span> and become the{' '}
                <span className="font-semibold text-[#0E2432]">best version</span> of yourself{' '}
              </IconWithDescription>
            </div>
            <div className="relative w-full z-20 flex items-center">
              <div className="absolute inset-x-0 -bottom-[5px] w-full md:w-[300px] h-[30%] rounded-full bg-[#37aef3] blur-[20px] opacity-100"></div>
              <Link
                href="/love-languages/test"
                className="font-switzer font-semibold shadow-[0px_1px_1px_rgba(35,_123,_255,_0.7)] w-full md:w-[300px] bg-[#5DC4FF] rounded-[10px] flex items-center justify-center px-[40px] py-[20px] tracking-[0.03em] text-white leading-[24px] text-[16px] md:text-[20px] relative z-10">
                <button>Take the Test</button>
              </Link>
            </div>
          </div>
          <div className="relative flex items-center justify-center md:hidden top-[-54px]">
            <Image
              src="/images/love-languages/photos-mobile.png"
              alt="Mobile photos"
              width={329}
              height={342}
              className="absolute w-full h-auto pl-[23px] pr-[33px] z-10"
            />
            <Image
              src="/images/love-languages/gradient-mobile.png"
              alt="Mobile Gradient"
              width={375}
              height={527}
              className="w-full h-auto z-0 mt-[-36px]"
            />
          </div>
        </div>
        <div className="evolution-section relative w-full m-0 p-0 px-[16px] md:py-[80px] md:px-[82px] mb-[60px] md:mb-[0px] mt-[-76px] md:mt-0">
          <div className="absolute inset-0 flex justify-center items-center z-10">
            <Image
              src="/images/love-languages/evolution-desktop.png"
              alt="Desktop Gradient"
              width={944}
              height={873}
              className="hidden md:block mt-[172px] w-auto z-10"
              style={{ height: 'calc(100% - 60px)' }}
              priority
            />
          </div>

          <label className="font-raleway font-bold flex justify-center text-center text-[32px] leading-[36px] md:text-[56px] md:leading-[66px] tracking-[-0.03em] md:mb-[40px] mb-[26px] z-10 text-[#0E2432] z-20">
            The Evolution <br />
            of Love Language
          </label>

          <div className="relative grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-5 z-20">
            <Image
              src="/images/love-languages/evolution-mobile-1.png"
              alt="Evolution Mobile 1"
              width={228}
              height={428}
              className="opacity-90 block md:hidden absolute right-[-16px] z-10"
              style={{ top: 'calc(25 - 252px)' }}
            />
            <BoxWithIcon
              source="/images/love-languages/origin.svg"
              alt="Origin Icon"
              heading="The Origins of Love Languages">
              Over 30 years ago, Dr. Gary Chapman introduced the &apos;
              <span className="font-sans">5</span> Love Languages&apos; test, helping millions improve their
              relationships. However, this concept originated in the <span className="font-sans">1980</span>s and is now
              viewed as limited by some, primarily catering to married, heterosexual, Christian couples.
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/love-languages/limitation.svg"
              alt="Limitation Icon"
              heading="Limitations of the Original Love Languages">
              The original <span className="font-sans">5</span> Love Languages were created for a specific demographic
              and time. But relationships today are diverse, complex, and evolving. Our expanded approach goes beyond
              traditional models to offer a more inclusive, nuanced understanding of how people connect and feel loved.
            </BoxWithIcon>
            <Image
              src="/images/love-languages/evolution-mobile-2.png"
              alt="Evolution Mobile 2"
              width={269}
              height={414}
              className="opacity-90 block md:hidden absolute left-[-16px] z-10"
              style={{ top: 'calc(75% - 208px)' }}
            />
            <BoxWithIcon
              source="/images/love-languages/love.svg"
              alt="Love Icon"
              heading="A New, Deeper Way to Understand Love">
              Our <span className="font-sans">7</span> Love Styles Test goes beyond traditional models, filling in the
              gaps left by the original approach. This comprehensive test embraces emotional, intellectual, and physical
              dimensions of love, offering a richer, more complete picture of how people connect and express affection.
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/love-languages/spectrum.svg"
              alt="Spectrum Icon"
              heading="Capturing Full Spectrum of Love Languages">
              Our model builds on the foundation of the <span className="font-sans">5</span> Love Languages, expanding
              it to reflect the diverse ways love is shared in today&apos;s world. This test provides personalized
              insights, helping you understand how you and others experience and express love across a wider range of
              dimensions.
            </BoxWithIcon>
          </div>
        </div>
        <Footer />
      </main>
    </div>
  );
};

export default LandingPage;
