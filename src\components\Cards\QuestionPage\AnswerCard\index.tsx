import { useContext, useState, useEffect } from 'react';
import Image from 'next/image';
import SessionContext from '@/store/SessionContext';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

const AnswerCard = ({
  questionId,
  answerId,
  numberOfAnswers,
}: {
  questionId: number;
  answerId: number;
  numberOfAnswers: number;
}) => {
  const { captureEvent } = usePostHogAnalytics();

  const { answers, updateAnswer, updateQuestionId, siteConfig } = useContext(SessionContext);
  const [borderColor, setBorderColor] = useState<string>('transparent');

  useEffect(() => {
    answers.find(ans => ans.questionId === questionId)?.answerId === answerId
      ? setBorderColor(siteConfig.primaryColor)
      : setBorderColor('transparent');
  }, [answers, questionId, answerId, siteConfig]);

  const handleClick = () => {
    captureEvent(PostHogEventEnum.QUESTION_ANSWERED, { questionId, answerId });
    updateAnswer({ questionId, answerId });
    if (questionId < 30) {
      updateQuestionId(questionId + 1);
    }
  };

  return (
    <div
      key={answerId}
      className={`answer-card text-center ${
        numberOfAnswers === 6
          ? 'w-[calc(28%+2px)] xxs:w-[calc(30%+2px)]  md:w-[calc(45%+2px)]'
          : 'w-[calc(45%-0px)] sm:w-[calc(38%-0px)]'
      } mdh:w-[28%] xlgh:w-[calc(26%+2px)] xlh:w-[calc(28%+2px)] xl:w-[calc(33.33%-20px)] 2xl:w-[calc(33.33%-18px)] ${
        answerId < (numberOfAnswers * 2) / 3 ? '2xl:mb-[18px]' : '2xl:mb-0'
      } ${
        numberOfAnswers === 4 && answerId === 1 ? 'md:ml-10 lg:ml-20 xl:ml-40' : ''
      } mr-2 mb-2 2xl:mr-[18px] p-6 pb-0`}
      onClick={handleClick}
      style={{
        border: `2px solid ${borderColor}`,
        boxShadow:
          '-3.563px 7.126px 12.471px 0px rgba(104, 129, 177, 0.08), -0.891px 0.891px 0.891px 0.891px rgba(141, 160, 188, 0.08)',
        cursor: 'pointer',
        //position: 'relative',
      }}>
      <Image
        src={`/questions/${questionId}/answers/${answerId}.png`}
        alt={`Image of IQ test answer for question.`}
        width={120}
        height={120}
        className="sm:p-2 sm:border-[12px] 2xl:p-3 2xl:border-[16px] border-solid border-grey-bg w-auto m-auto transition-opacity opacity-0 duration-[1s]"
        unoptimized
        onLoad={e => e.currentTarget.classList.remove('opacity-0')}
        priority
      />
      <span className="font-semibold" style={{ color: '#191919', fontSize: 20, lineHeight: '180%' }}>
        {(answerId + 9).toString(36)}
      </span>
    </div>
  );
};

export default AnswerCard;
