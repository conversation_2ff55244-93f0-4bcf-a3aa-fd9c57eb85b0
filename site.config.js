const getCompanyInfo = require('./companies.config.js');

const siteConfig = {
  siteName: 'International IQ',
  domain: 'iqinternational.org',
  supportEmail: '<EMAIL>',
  hostingProvider: 'Vercel Inc.',
  gtmId: 'GTM-WRTN3SWB',
  companyName: null,
  companyAddress: null,
  companyRegistrationNumber: null,
  companyPhone: null,
  euVat: null,
  logo: {
    path: '/images/Logo.svg',
    width: 300,
    height: 36,
  },
  primaryColor: '#ff932f',
  insights: {
    show: true,
    url: 'https://blog.iqinternational.org',
  },
  fonts: {
    src: [
      {
        path: './fonts/PPMori-Regular.woff2',
        weight: '400',
        style: 'normal',
      },
      {
        path: './fonts/PPMori-SemiBold.woff2',
        weight: '600',
        style: 'normal',
      },
    ],
  },
};

function getSiteConfig(country) {
  return { ...siteConfig, ...getCompanyInfo(country) };
}

module.exports = { getSiteConfig, siteConfig };
