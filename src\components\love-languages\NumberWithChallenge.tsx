import Image from "next/image";
import React, { ReactNode } from "react";
import { useState, useEffect } from "react";

type NumberWithChallengeProps = {
  sequence: number;
  source: string;
  alt: string;
  heading: string;
  children?: ReactNode;
};

const NumberWithChallenge: React.FC<NumberWithChallengeProps> = ({
  sequence,
  source,
  alt,
  heading,
  children,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const updateIsMobile = () => {
      if (typeof window !== "undefined") {
        setIsMobile(!window.matchMedia("(min-width: 768px)").matches);
      }
    };

    updateIsMobile(); // Initial check on mount
    window.addEventListener("resize", updateIsMobile);

    return () => {
      window.removeEventListener("resize", updateIsMobile);
    };
  }, []);

  const size = isMobile
    ? 84
    : 110;

  return (
    <div
      className={`flex flex-col gap-4 md:gap-0 ${
        sequence % 2 == 1 ? "md:flex-row" : "md:flex-row-reverse"
      } justify-center`}
    >
      <div className="relative">
        <h4
          className="font-raleway font-black text-[80px] leading-[70px] md:text-[146px] md:leading-[132px] text-[#5DC4FF]"
          style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}
        >
          0{sequence}
        </h4>
        <Image
          src={source}
          alt={alt}
          width={size}
          height={size}
          className={`absolute left-[6px] ${
            sequence % 2 == 1 ? "md:left-[56px]" : "md:left-[30px]"
          } w-[84px] md:w-[110px] h-auto bottom-[-20px] md:bottom-[-20px]`}
        />
      </div>
      <div className="flex flex-col gap-2 md:pl-[20px] md:py-[10px]">
        <h5
          className="font-raleway font-bold text-[#0E2432] text-[20px] md:text-[24px] leading-[26px] md:leading-[32px]"
          dangerouslySetInnerHTML={{
            __html: heading,
          }}
        ></h5>
        <p className="font-raleway font-medium text-[#828E98] md:text-[16px] md:leading-[24px]">
          {children}
        </p>
      </div>
    </div>
  );
};

export default NumberWithChallenge;
