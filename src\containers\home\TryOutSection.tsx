import Image from 'next/image';
import { useTranslations } from 'next-intl';
import CtaButton from '@/components/Buttons/CtaButton';

const TryOutSection = () => {
  const t = useTranslations('home_page');
  
  const statList = [
    { title: 1802, text: t('label_peopletoday') },
    { title: 108, text: t('label_avgtoday') },
    { title: 138, text: t('label_highesttoday') },
  ];

  return (
    <section
      className='grid lg:flex lg:wrap lg:justify-center pb-0 lg:pb-24'
      style={{
        background: 'linear-gradient(180deg, rgba(246, 249, 255, 0.30) 0%, #F6F9FF 100%)',
        paddingLeft: 0,
        paddingRight: 0,
      }}
    >
      <div
        className='order-2 lg:order-1 w-full lg:w-1/2 relative lg:max-w-[520px] h-[500px] lg:h-auto pt-20 lg:mt-0 pl-24 xl:pl-[200px] scale-[0.6] md:scale-75 lg:scale-100'
        style={{
          backgroundImage: 'url(/home/<USER>/checkered-min.png)',
        }}
      >
        <Image
          style={{
            zIndex: 1,
            rotate: '-4.33deg',
            border: '0.828px solid rgba(193, 207, 233, 0.45)',
            boxShadow: '-4px 8px 14px 0px rgba(104, 129, 177, 0.08), -1px 1px 1px 1px rgba(141, 160, 188, 0.10)',
          }}
          className='absolute top-0 lg:top-[100px] left-[calc(50%-160px)] sm:left-[calc(50%-201px)] lg:left-[calc(50%-101px)]'
          src={'/home/<USER>/first-card.png'}
          alt='Decoration in question card style'
          width={374}
          height={390}
        />
        <Image
          style={{
            rotate: '-15deg',
            boxShadow: '1px 1px 1px 1px rgba(141, 160, 188, 0.08)',
            border: '0.828px solid rgba(214, 218, 225, 0.45)',
          }}
          className='absolute top-[3px] lg:top-[103px] left-[calc(50%-209px)] sm:left-[calc(50%-250px)] lg:left-[calc(50%-150px)]'
          src={'/home/<USER>/background-card.png'}
          alt='Another example image in the background'
          width={374}
          height={405}
        />
        <Image
          style={{
            zoom: 0.8,
            zIndex: 2,
          }}
          className='absolute top-[415px] lg:top-[547px] left-[calc(50%-295px)] sm:left-[calc(50%-353px)] lg:left-[calc(50%-253px)]'
          src={'/home/<USER>/wave.svg'}
          alt='Small wave for decoration in try out section'
          width={216}
          height={206}
        />
      </div>
      <div
        className='order-1 lg:order-2 w-full lg:w-1/2 relative lg:max-w-[520px] lg:ml-32 pb-30 px-[5.69444%] lg:px-0'
        style={{}}
      >
        <h2 className='big'>{t('try_out_title')}</h2>
        <p style={{ padding: '24px 0 50px 0' }}>
          {t('try_out_description')}
        </p>
        <div
          className='flex flex-wrap max-w-[90vw] justify-center xs:justify-start sm:justify-end gap-5 lg:justify-start xl:gap-0 xl:justify-between font-semibold text-center xs:text-left'
          style={{}}
        >
          {statList.map((item) => (
            <div key={item.title}>
              <span style={{ fontSize: 44, color: '#191919' }}>{item.title}</span>
              <div style={{ width: 120, fontSize: 18, color: '#191919' }}>{item.text}</div>
            </div>
          ))}
        </div>
        <CtaButton
          className='md:block float-right lg:float-left mt-8 lg:mt-16'
          style={{}}
          {...{ type: 'primary' }}
        />
      </div>
    </section>
  );
};

export default TryOutSection;
