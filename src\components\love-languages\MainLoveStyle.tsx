import React from 'react';

type MainLoveStyleProps = {
  title: string;
  description: string;
};

const MainLoveStyle: React.FC<MainLoveStyleProps> = ({ title, description }) => {
  return (
    <>
      <p className="font-raleway font-bold text-[#5DC4FF] text-[36px] md:text-[64px] leading-[40px] md:leading-[74px] tracking-[-0.03em] mb-4 md:mb-6 z-20">
        {title}
      </p>
      <p className="font-raleway font-medium text-[#828E98] text-[16px] md:text-[18px] leading-[22px] md:leading-[27px] z-20">
        {description}
      </p>
    </>
  );
};

export default MainLoveStyle;
