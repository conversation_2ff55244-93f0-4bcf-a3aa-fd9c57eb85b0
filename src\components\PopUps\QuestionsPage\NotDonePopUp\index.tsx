import { memo } from 'react';
import Image from 'next/image';
import { Link } from '@/lib/i18n/navigation';
import Modal from 'react-modal';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

const NotDonePopUp = ({
  visible,
  setVisible,
}: {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { captureEvent } = usePostHogAnalytics();

  return (
    <Modal
      isOpen={visible}
      //onAfterOpen={afterOpenModal}
      //onRequestClose={closeModal}
      //style={customStyles}
      ariaHideApp={false}
      shouldCloseOnOverlayClick={true}
      onRequestClose={() => {
        setVisible(false);
      }}
      contentLabel="I am not done"
      className=""
      style={{
        content: {
          bottom: 'auto',
          minHeight: '10rem',
          left: 'calc(50% - 10px)',
          padding: '2rem',
          position: 'fixed',
          right: 'auto',
          top: '50%',
          transform: 'translate(-50%,-50%)',
          minWidth: '20rem',
          width: 'calc(95% - 20px)',
          maxWidth: 547,
          margin: 10,
          borderRadius: 16,
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          textAlign: 'center',
        },
        /*content: {
          top: 218,
          left: 'calc(50% - 273.5px)',
          width: 547,
          height: 464,
          borderRadius: 16,
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          textAlign: 'center',
        },*/
      }}>
      <Image
        src={`/questions/popup.svg`}
        alt={`Illustration for popup, if test is not done yet.`}
        width={203}
        height={136}
        priority
        //style={{ height: 'fit-content', maxWidth: 'fit-content', border: '24px solid #F6F9FF', padding: 12 }}
      />
      <h4 className="big" style={{ marginTop: 32, maxWidth: 400 }}>
        You Haven’t Answered All the Questions
      </h4>
      <p style={{ marginTop: 20, maxWidth: 480 }}>
        Submitting your answers now can affect your overall result. Are you sure you want to finish the test?
      </p>
      <div
        className="flex flex-wrap justify-center popupMd:justify-between gap-4"
        style={{ marginTop: 32, padding: '0 21.5px', width: '100%' }}>
        <div>
          <button
            onClick={() => {
              captureEvent(PostHogEventEnum.DECLINED_GET_RESULTS, {
                description: 'Declined to get results with some skipped questions',
              });
              setVisible(false);
            }}
            className={`button primaryColor font-semibold !text-lg/6 xs:!text-xl/6 text-primary border border-primary`}
            style={{
              padding: '17px 32px',
              display: 'inline-flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              background: '#fff',
              borderRadius: 10,
              letterSpacing: '-0.6px',
            }}>
            No, take me back
          </button>
        </div>
        <div>
          <Link
            href={`/calculation?origin=questions`}
            className={`button primaryColor font-semibold bg-primary border border-primary`}
            style={{
              //padding: '16px 40px',
              padding: '17px 32px',
              display: 'inline-flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              borderRadius: 10,
              fontSize: 20,
              color: '#fff',
              lineHeight: '120%',
              letterSpacing: '-0.6px',
            }}>
            <button
              onClick={() =>
                captureEvent(PostHogEventEnum.CONFIRMED_GET_RESULTS, {
                  description: 'Confirmed to get results with some skipped questions',
                })
              }>
              {"Yes, I'm done"}
            </button>
          </Link>
        </div>
      </div>
    </Modal>
  );
};

export default memo(NotDonePopUp);
