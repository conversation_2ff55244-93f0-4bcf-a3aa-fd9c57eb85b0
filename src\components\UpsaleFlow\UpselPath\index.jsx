import Image from 'next/image';
import Brand from '../../Brand';

export default function UpselPath({ section }) {
  return (
    <div className=" w-[90%] md:w-[500px] relative mx-auto mt-12 md:mt-12">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t-2 border-orange-500">
          <div className="relative top-[-15px]">
            <div>
              <div className="rounded-full w-[28px] h-[28px] border-2 bg-white relative  border-orange-400">
                <div
                  className={`bg-primary rounded-full m-auto  ${
                    section !== 1 ? 'w-[24px] h-[24px]' : 'w-[18px] h-[18px] mt-[3px]'
                  }`}>
                  {section !== 1 && (
                    <svg
                      className="relative top-[8px] left-[6px]"
                      width="13"
                      height="8"
                      viewBox="0 0 13 8"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M10.2077 0.270363C10.6431 -0.0901209 11.3491 -0.0901209 11.7845 0.270363C12.2146 0.626425 12.2199 1.201 11.8004 1.56243L5.86525 7.70428C5.85669 7.71314 5.84753 7.72161 5.83784 7.72964C5.4024 8.09012 4.69642 8.09012 4.26098 7.72964L0.326578 4.47247C-0.108859 4.11199 -0.108859 3.52753 0.326578 3.16704C0.762014 2.80656 1.468 2.80656 1.90343 3.16704L5.01582 5.74368L10.1781 0.297975C10.1873 0.288289 10.1972 0.279069 10.2077 0.270363Z"
                        fill="white"
                      />
                    </svg>
                  )}
                </div>
              </div>
              <p className="absolute text-[12px] md:text-[16px] text-[#191919] w-[100px] md:w-auto left-[-15px] md:left-[-20px] top-10">
                Skip trial
              </p>
            </div>
            <div className=" absolute top-0 left-[31%]">
              <div className="rounded-full w-[28px] h-[28px] border-2 bg-white border-orange-400">
                {section !== 1 && (
                  <div
                    className={`bg-primary rounded-full m-auto ${
                      section == 5 ? 'w-[24px] h-[24px]' : 'w-[18px] h-[18px] mt-[3px]'
                    }`}>
                    {section == 5 && (
                      <svg
                        className="relative top-[8px] left-[6px]"
                        width="13"
                        height="8"
                        viewBox="0 0 13 8"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M10.2077 0.270363C10.6431 -0.0901209 11.3491 -0.0901209 11.7845 0.270363C12.2146 0.626425 12.2199 1.201 11.8004 1.56243L5.86525 7.70428C5.85669 7.71314 5.84753 7.72161 5.83784 7.72964C5.4024 8.09012 4.69642 8.09012 4.26098 7.72964L0.326578 4.47247C-0.108859 4.11199 -0.108859 3.52753 0.326578 3.16704C0.762014 2.80656 1.468 2.80656 1.90343 3.16704L5.01582 5.74368L10.1781 0.297975C10.1873 0.288289 10.1972 0.279069 10.2077 0.270363Z"
                          fill="white"
                        />
                      </svg>
                    )}
                  </div>
                )}
                <svg
                  className="relative top-[8px] left-[6px]"
                  width="13"
                  height="8"
                  viewBox="0 0 13 8"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M10.2077 0.270363C10.6431 -0.0901209 11.3491 -0.0901209 11.7845 0.270363C12.2146 0.626425 12.2199 1.201 11.8004 1.56243L5.86525 7.70428C5.85669 7.71314 5.84753 7.72161 5.83784 7.72964C5.4024 8.09012 4.69642 8.09012 4.26098 7.72964L0.326578 4.47247C-0.108859 4.11199 -0.108859 3.52753 0.326578 3.16704C0.762014 2.80656 1.468 2.80656 1.90343 3.16704L5.01582 5.74368L10.1781 0.297975C10.1873 0.288289 10.1972 0.279069 10.2077 0.270363Z"
                    fill="white"
                  />
                </svg>
              </div>

              <p className="absolute text-[12px] md:text-[16px] text-[#191919] w-[100px] left-[-25px] md:left-[-30px] top-10 md:w-[100px]">
                Add packs
              </p>
            </div>
            <div className=" absolute top-0 left-[62%]">
              <div className="rounded-full w-[28px] h-[28px] border-2 bg-white relative  border-orange-400">
                {section > 1 && <div className="bg-primary rounded-full w-[18px] h-[18px] m-auto mt-[3px]"></div>}
              </div>
              <p className="absolute text-[12px] md:text-[16px] text-[#191919] left-[-30px] md:left-[-40px] top-10 w-[100px] md:w-[120px]">
                Offline mode
              </p>
            </div>
            <div className=" absolute top-0 right-[0px]">
              <div className="rounded-full w-[28px] h-[28px] border-2 bg-white relative  border-orange-400"></div>
              <p className="absolute text-[12px] md:text-[16px] text-[#191919] left-[-40px] md:left-[-40px] top-10 w-[150px] md:w-[140px]">
                Access product
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
