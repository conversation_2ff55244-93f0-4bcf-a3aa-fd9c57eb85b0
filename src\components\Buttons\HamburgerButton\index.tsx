'use client';

import { CloseIcon } from '@/components/Icons/MobileMenu/Close';
import { HamburgerIcon } from '@/components/Icons/MobileMenu/Hamburger';
import useLayoutStore from '@/store/useLayoutStore';
import type { FC } from 'react';

interface HamburgerButtonProps {}

const HamburgerButton: FC<HamburgerButtonProps> = ({}) => {
  const { mobileOpen, handleMobileOpen } = useLayoutStore();
  return (
    <div
      className='cursor-pointer p-[10px] rounded-[10px] ml-3 z-20'
      style={{ border: '1px solid rgba(193, 207, 233, 0.45)' }}
      onClick={() => handleMobileOpen(!mobileOpen)}
    >
      {mobileOpen ? <CloseIcon /> : <HamburgerIcon />}
    </div>
  );
};

export default HamburgerButton;
