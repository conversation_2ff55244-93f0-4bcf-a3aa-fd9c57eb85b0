import Image from 'next/image';
import React from 'react';

const PaymentCards: React.FC = () => {
  return (
    <div className={`flex items-center gap-[8px] md:gap-[12px] bg-[#FDFEFF] mb-[24px] md:mb-[32px]`}>
      <Image
        src="/images/emotional-intelligence/visa1.svg"
        alt="visa1"
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa2.svg"
        alt="visa2"
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa3.svg"
        alt="visa3"
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa4.svg"
        alt="visa4"
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
      <Image
        src="/images/emotional-intelligence/visa5.svg"
        alt="visa5"
        width={65}
        height={40}
        className={`w-[45px] md:w-[65px] h-auto`}
      />
    </div>
  );
};

export default PaymentCards;
