import React from 'react';
import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, Legend } from 'chart.js';
import dynamic from 'next/dynamic';

type PiechartProps = {
  results: Array<{
    category: string;
    sum: number;
    percentage: number;
  }>;
};

ChartJS.register(ArcE<PERSON>, Toolt<PERSON>, Legend);

const Pie = dynamic(() => import('react-chartjs-2').then(mod => mod.Pie), {
  ssr: false,
});

const colors: Record<string, string> = {
  'Caring Gestures': '#5DC4FF',
  'Loving Words': '#FF5D5D',
  'Touch of Affection': '#FFAB5D',
  'Nourishing Care': '#D06DFF',
  'Meaningful Gifts': '#48E129',
  'Deep Understanding': '#FF5DDB',
  'Shared Experiences': '#605DFF',
};

const Piechart: React.FC<PiechartProps> = ({ results }) => {
  const pieData = {
    labels: results?.map(style => style.category),
    datasets: [
      {
        data: results?.map(style => style.percentage),
        backgroundColor: results?.map(style => colors[style.category]),
        borderWidth: 4,
      },
    ],
  };
  return (
    <div className="w-full flex flex-col md:flex-row items-center justify-center gap-[32px] md:gap-[38px]">
      <div className="relative w-full md:w-[500px] md:h-[500px] aspect-square">
        <Pie
          data={pieData}
          options={{
            plugins: { legend: { display: false } },
            maintainAspectRatio: true,
            cutout: '20%',
          }}
        />
        {/* <div className="absolute inset-0 flex justify-center items-center">
        <Image
          src="/images/love-languages/avatar.png"
          alt="Avatar Picture"
          width={220}
          height={220}
          className="w-2/5 h-auto rounded-full object-cover"
        />
      </div> */}
      </div>
      <div className="w-full flex flex-col gap-4">
        {results?.map(style => (
          <div
            key={style.category}
            className="flex flex-col gap-[10px] py-4 px-5 rounded-[12px]"
            style={{ backgroundColor: `${colors[style.category]}0D` }}>
            <div className="flex flex-row gap-3 items-center">
              <span className="font-raleway font-bold text-[24px] leading-[32px] text-[#0E2432]">
                {style.category}:
              </span>
              <span
                className="font-raleway font-bold text-[24px] leading-[32px]"
                style={{
                  color: colors[style.category],
                  fontFeatureSettings: "'pnum' on, 'lnum' on",
                }}>
                {style.percentage}%
              </span>
            </div>
            <div className="h-2 rounded-full" style={{ backgroundColor: `${colors[style.category]}1A` }}>
              <div
                className="h-2 rounded-full relative"
                style={{
                  width: `${style.percentage}%`,
                  backgroundColor: colors[style.category],
                }}>
                <div
                  className={`absolute top-1/2 right-0 transform -translate-y-1/2 rounded-full border-[3px] border-white bg-[${
                    colors[style.category]
                  }] shadow-md`}
                  style={{
                    width: '14px', // Circle width
                    height: '14px', // Circle height
                  }}></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Piechart;
