'use client';
import { Link } from '@/lib/i18n/navigation';
import type { FC } from 'react';

interface LoginButtonProps {
  className?: string;
  style?: any;
}

const LoginButton: FC<LoginButtonProps> = ({ className, style }) => {
  return (
    <Link
      href={`/login`}
      className={`text-center inline-flex justify-center w-full sm:w-40 button secondary ${className} text-sm md:text-xl font-semibold ml-3`}
      style={{
        borderRadius: 10,
        lineHeight: '120%',
        ...style,
      }}
    >
      Login
    </Link>
  );
};

export default LoginButton;
