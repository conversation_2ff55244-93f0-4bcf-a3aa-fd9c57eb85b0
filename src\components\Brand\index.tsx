"use client";
import { FC, memo, useContext } from 'react';
import { Link } from '@/lib/i18n/navigation';
import Image from 'next/image';
import SessionContext from '@/store/SessionContext';

interface BrandProps {
  style?: any;
  classes?: string;
  position?: string;
}

const Brand: FC<BrandProps> = ({ style, classes, position }) => {
  const { siteConfig } = useContext(SessionContext);
  return (
    <Link
      href='/'
      className={`flex gap-3 items-center ${position !== 'title' ? 'mr-5' : 'justify-center'} ${classes}`}
    >
      <Image src={siteConfig.logo.path} alt='Logo' width={siteConfig.logo.width} height={siteConfig.logo.height} quality={100} priority />
    </Link>
  );
};

export default memo(Brand);
