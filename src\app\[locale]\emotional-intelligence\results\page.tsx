'use client';
import Account from '@/components/emotional-intelligence/Account';
import Footer from '@/components/emotional-intelligence/landing/GFooter';
import Logo from '@/components/emotional-intelligence/landing/Logo';
import BoxWithIcon from '@/components/emotional-intelligence/results/BoxWithIcon';
import Box<PERSON>ithNumber from '@/components/emotional-intelligence/results/BoxWithNumber';
import CardWithGaugeChart from '@/components/emotional-intelligence/results/CardWithGaugeChart';
import HeaderContent from '@/components/emotional-intelligence/results/HeaderContent';
import { useRouter } from '@/lib/i18n/navigation';
import { addFormDataToSessionDb } from '@/services/session';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { isGTMInitialized } from '@/utils/isGtmInitialized';
import { sendGTMEvent } from '@next/third-parties/google';
import { ArcElement, Chart as ChartJS, Legend, Tooltip } from 'chart.js';
import { isEmpty } from 'lodash';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import React, { useContext, useEffect, useState } from 'react';
ChartJS.register(ArcElement, Tooltip, Legend);

const Results: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const showResults = searchParams.get('showResults');
  const isShowResults = showResults === 'true';
  const { emotionalScores, fetchAllSessionData, checkoutId, prices, resetAnswers } = useContext(SessionContext) ?? {};
  const { time } = useContext(UiContext);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [totalTitle, setTotalTitle] = useState<string>('');
  const [totalDescription, setTotalDescription] = useState<string>('');

  const [totalChartTitle, setTotalChartTitle] = useState<string>('');
  const [totalChartDescription, setTotalChartDescription] = useState<string>('');

  const [neurodivergentDescription, setNeurodivergentDescription] = useState<string>('');
  const [mentalDescription, setMentalDescription] = useState<string>('');
  const [impactDescription, setImpactDescription] = useState<string>('');

  const [growthArray, setGrowthArray] = useState<{ title: string; description: string }[]>([]);

  const [conclusionTitle, setConclusionTitle] = useState<string>('');
  const [conclusionDescription, setConclusionDescription] = useState<string>('');

  const [awarenessTitle, setAwarenessTitle] = useState<string>('');
  const [awarenessDescription, setAwarenessDescription] = useState<string>('');

  const [regulationTitle, setRegulationTitle] = useState<string>('');
  const [regulationDescription, setRegulationDescription] = useState<string>('');

  const [relationshipTitle, setRelationshipTitle] = useState<string>('');
  const [relationshipDescription, setRelationshipDescription] = useState<string>('');
  const sourceFromUrl = searchParams.get('source');
  const [empathyTitle, setEmpathyTitle] = useState<string>('');
  const [empathyDescription, setEmpathyDescription] = useState<string>('');

  const [adaptabilityTitle, setAdaptabilityTitle] = useState<string>('');
  const [adaptabilityDescription, setAdaptabilityDescription] = useState<string>('');

  useEffect(() => {
    if (isEmpty(emotionalScores)) {
      router.push('/emotional-intelligence/test');
    }
    const fetchData = async () => {
      addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'emotional-intelligence', emotionalScores });
    };

    !isShowResults && fetchData();
  }, []);

  useEffect(() => {
    if (sourceFromUrl === 'qr') return;

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on Emotional Intelligence Results page: Event not sent');
      return;
    }

    //TODO: Move this GTM event to the checkout page when payment is successful
    sendGTMEvent({
      event: 'purchase',
      ecommerce: {
        currency: prices.currency.toUpperCase(),
        value: prices.oneTime.amount,
        transaction_id: checkoutId, // generate a unique ID for each purchase
        items: [
          {
            item_name: 'EI test',
            item_id: 'eitest',
            price: prices.oneTime.amount.toString(),
            quantity: 1,
          },
        ],
      },
    });
    
    resetAnswers();
  }, [sourceFromUrl, checkoutId, prices]);

  useEffect(() => {
    let totalSum = 0;

    Object.entries(emotionalScores).forEach(([category, score]) => {
      totalSum += score;
      if (score >= 48 && score <= 68) {
        if (category === 'Emotional Self-Awareness') {
          setAwarenessTitle('High Awareness');
          setAwarenessDescription(
            'You have a strong understanding of your emotions and are reflective about your emotional states.'
          );
        } else if (category === 'Self-Regulation') {
          setRegulationTitle('Highly Controlled');
          setRegulationDescription(
            'You manage your emotions well, even in stressful situations, and maintain composure effectively.'
          );
        } else if (category === 'Empathy and Social Awareness') {
          setEmpathyTitle('Highly Empathetic');
          setEmpathyDescription("You excel at understanding others' emotions and responding with empathy and care.");
        } else if (category === 'Relationship Management') {
          setRelationshipTitle('Strong Relationship Skills');
          setRelationshipDescription(
            'You manage relationships well, communicating effectively and resolving conflicts constructively.'
          );
        } else if (category === 'Adaptability and Resilience') {
          setAdaptabilityTitle('Highly Adaptable and Resilient');
          setAdaptabilityDescription(
            'You handle change and setbacks well, quickly bouncing back and adjusting your emotions accordingly.'
          );
        }
      } else if (score >= 36 && score <= 47) {
        if (category === 'Emotional Self-Awareness') {
          setAwarenessTitle('Moderate Awareness');
          setAwarenessDescription(
            'You are often aware of your emotions but may sometimes struggle to identify the root causes of your feelings.'
          );
        } else if (category === 'Self-Regulation') {
          setRegulationTitle('Moderately Controlled');
          setRegulationDescription(
            'You usually handle your emotions well but may find it challenging to stay composed in highly stressful moments.'
          );
        } else if (category === 'Empathy and Social Awareness') {
          setEmpathyTitle('Moderately Empathetic');
          setEmpathyDescription(
            "You are generally good at reading others' emotions but may occasionally miss subtle cues."
          );
        } else if (category === 'Relationship Management') {
          setRelationshipTitle('Adequate Relationship Skills');
          setRelationshipDescription(
            'You are good at maintaining relationships but may encounter challenges during conflicts or emotional misunderstandings.'
          );
        } else if (category === 'Adaptability and Resilience') {
          setAdaptabilityTitle('Moderately Adaptable and Resilient');
          setAdaptabilityDescription(
            'You are adaptable but may need more time to recover from setbacks or adjust to unexpected changes.'
          );
        }
      } else if (score >= 24 && score <= 35) {
        if (category === 'Emotional Self-Awareness') {
          setAwarenessTitle('Low Awareness');
          setAwarenessDescription(
            'You occasionally understand your emotions but often feel disconnected or unsure of why you feel the way you do.'
          );
        } else if (category === 'Self-Regulation') {
          setRegulationTitle('Low Control');
          setRegulationDescription(
            'Emotional regulation is inconsistent; you may find it hard to manage your reactions in difficult situations.'
          );
        } else if (category === 'Empathy and Social Awareness') {
          setEmpathyTitle('Low Empathetic');
          setEmpathyDescription(
            "Understanding others' emotions can be challenging, and you may feel disconnected in social interactions."
          );
        } else if (category === 'Relationship Management') {
          setRelationshipTitle('Weak Relationship Skills');
          setRelationshipDescription(
            'Building and maintaining positive relationships can be difficult, especially during emotional conflicts.'
          );
        } else if (category === 'Adaptability and Resilience') {
          setAdaptabilityTitle('Low Adaptable and Resilient');
          setAdaptabilityDescription(
            'Change and setbacks can be challenging for you, often leading to prolonged emotional impacts.'
          );
        }
      } else if (score >= 12 && score <= 23) {
        if (category === 'Emotional Self-Awareness') {
          setAwarenessTitle('Very Low Awareness');
          setAwarenessDescription(
            'You frequently struggle to identify your emotions, making emotional self-awareness a key area for growth.'
          );
        } else if (category === 'Self-Regulation') {
          setRegulationTitle('Very Low Control');
          setRegulationDescription(
            'You often struggle to control your emotions, leading to impulsive reactions or emotional overwhelm.'
          );
        } else if (category === 'Empathy and Social Awareness') {
          setEmpathyTitle('Very Low Empathetic');
          setEmpathyDescription(
            "You struggle significantly with recognizing and responding to others' emotions, making empathy a key area for development."
          );
        } else if (category === 'Relationship Management') {
          setRelationshipTitle('Very Weak Relationship Skills');
          setRelationshipDescription(
            'You often struggle with relationship management, finding it hard to communicate or resolve conflicts positively.'
          );
        } else if (category === 'Adaptability and Resilience') {
          setAdaptabilityTitle('Very Low Adaptable and Resilient');
          setAdaptabilityDescription(
            'You frequently struggle with change, and emotional setbacks can significantly affect your mood and behavior.'
          );
        }
      }
    });

    setTotalScore(totalSum);
  }, [emotionalScores]);

  useEffect(() => {
    if (totalScore >= 240 && totalScore <= 300) {
      setTotalTitle('High Emotional Intelligence');
      setTotalDescription(
        'You scored in the highest range, indicating that you possess a high level of emotional intelligence. You demonstrate exceptional self-awareness, empathy, and emotional regulation. Your ability to understand and manage your own emotions, as well as recognize and respond to the emotions of others, sets you apart in both personal and professional settings. You are often seen as a calm, empathetic, and effective communicator who can navigate complex social dynamics with ease.'
      );
      setTotalChartTitle('You’ve mastered emotional intelligence but growth is limitless');
      setTotalChartDescription(
        'Continue leveraging your emotional insight to excel, while refining any areas where consistency can elevate your interactions'
      );
      setNeurodivergentDescription(
        'Your high emotional intelligence includes an awareness of how neurodivergence (such as ADHD, autism, or other cognitive differences) can shape emotional experiences. You are mindful that sensory sensitivities, social dynamics, or communication styles can vary, and you adapt your approach to ensure inclusivity and understanding in your interactions.'
      );
      setMentalDescription(
        'You recognize that emotional intelligence does not equate to always being positive or never experiencing emotional challenges. You are compassionate toward yourself and others regarding the impacts of mental health conditions such as anxiety, depression, or past trauma. Your EQ allows you to navigate these nuances without judgment, offering support and creating safe spaces for emotional expression.'
      );
      setImpactDescription(
        'You understand that past experiences, including trauma, can influence emotional reactions. You are sensitive to triggers and careful not to impose unrealistic expectations of emotional regulation on yourself or others. Your awareness of these factors makes you a considerate and supportive presence, especially for those who may struggle with emotional vulnerabilities.'
      );
      setGrowthArray([
        {
          title: 'Sustaining Your Emotional Intelligence',
          description:
            'While your emotional intelligence is high, remember that maintaining it involves ongoing self-reflection and learning. Emotional intelligence is dynamic, and life experiences can shift emotional needs and responses over time. Regular self-care, seeking feedback, and engaging in mindful practices can help you sustain your EQ levels.',
        },
        {
          title: 'Protecting Your Emotional Well-Being',
          description:
            'Be aware that despite your strong emotional skills, you are not immune to burnout or emotional fatigue, especially when frequently supporting others. Ensuring you set healthy boundaries and engage in activities that recharge your emotional energy is crucial.',
        },
        {
          title: 'Growing Through Awareness and Empathy',
          description:
            'Continue to be open to learning about how neurodivergent and mental health factors impact emotional expression and regulation. This awareness not only enhances your interactions but also broadens your empathy and understanding in diverse settings.',
        },
      ]);
      setConclusionTitle('You have High Emotional Intelligence');
      setConclusionDescription(
        'Your high level of emotional intelligence equips you with powerful tools for both personal fulfillment and professional success. Your awareness, empathy, and adaptability are invaluable, allowing you to positively influence those around you. Continue nurturing these strengths, and remember that your emotional well-being is as important as the support you offer to others.'
      );
    } else if (totalScore >= 180 && totalScore <= 239) {
      setTotalTitle('Emotional Intelligence');
      setTotalDescription(
        'Your score places you in the Emotionally Intelligent category, indicating that you have a strong grasp of your emotions and are generally effective at managing them. You demonstrate empathy, communicate well, and usually handle stress and conflict in a balanced manner. While you have a solid foundation in emotional intelligence, there are occasional areas where emotional regulation and awareness may pose challenges. This profile highlights your strengths while acknowledging opportunities for growth, particularly in maintaining consistency under pressure.'
      );
      setTotalChartTitle('You have strengths but also areas to improve');
      setTotalChartDescription(
        'Focus on bolstering weaker areas while leveraging your strengths to support overall growth in emotional intelligence'
      );
      setNeurodivergentDescription(
        'You are aware that neurodivergence, such as ADHD, autism, or other cognitive variations, can affect emotional processing and social interactions. You recognize that these differences can influence how emotions are expressed and perceived, and you are generally adaptable in accommodating varying needs. Enhancing your understanding of neurodivergent experiences can help you deepen your empathy and improve your interactions.'
      );
      setMentalDescription(
        'You acknowledge that factors like anxiety, depression, or other mental health conditions can affect emotional regulation. You strive to be compassionate with yourself and others, recognizing that emotional ups and downs are part of the human experience. Maintaining awareness of your own mental health and seeking support when needed can bolster your emotional resilience.'
      );
      setImpactDescription(
        'You understand that past traumas can shape emotional responses and may lead to heightened sensitivity in certain situations. You are mindful of your own triggers and are considerate of others’ experiences. However, there may be times when emotional memories resurface unexpectedly, impacting your current state. Engaging in practices that promote emotional healing, such as therapy or mindfulness, can help you manage these moments more effectively.'
      );
      setGrowthArray([
        {
          title: 'Consistency in Emotional Regulation',
          description:
            'While you manage emotions well most of the time, focusing on consistency, especially under pressure, will enhance your overall EQ. Techniques such as breathing exercises, journaling, or grounding practices can help you stay centered when emotions threaten to overwhelm.',
        },
        {
          title: 'Deepening Empathy and Social Awareness',
          description:
            'You already possess strong empathy skills, but continuing to actively listen and seek to understand others’ perspectives, especially those different from your own, will further strengthen your connections. Learning more about diverse emotional experiences, including those related to neurodivergence and mental health, can broaden your empathy.',
        },
        {
          title: 'Self-Care and Emotional Boundaries',
          description:
            'Ensuring that you prioritize self-care and set healthy emotional boundaries is key to maintaining your emotional intelligence. Balancing your own needs with your empathetic nature will help prevent burnout and allow you to sustain your supportive role in relationships.',
        },
        {
          title: 'Developing Coping Mechanisms',
          description:
            'Building a toolkit of coping mechanisms for stress and emotional turbulence will support your ongoing growth. Consider exploring techniques like cognitive-behavioral strategies, relaxation techniques, or creative outlets that allow you to process emotions constructively.',
        },
      ]);
      setConclusionTitle('You have Emotional Intelligence');
      setConclusionDescription(
        'Your score reflects a commendable level of emotional intelligence, with strengths in self-awareness, empathy, and relationship management. By continuing to build on these areas and addressing occasional challenges in emotional regulation, you can further enhance your emotional resilience and interpersonal effectiveness. Remember that emotional intelligence is a journey, and embracing both your strengths and areas for growth will empower you to navigate your emotions and relationships with increasing confidence and compassion.'
      );
    } else if (totalScore >= 120 && totalScore <= 179) {
      setTotalTitle('Moderate Emotional Intelligence');
      setTotalDescription(
        'Your score falls within the Moderate Emotional Intelligence category, indicating that you have a foundational awareness of your emotions and some ability to manage them. You often understand your feelings and can relate to others, but there are times when emotional regulation, stress management, and empathy can be challenging. This profile highlights your existing emotional skills and provides insights into areas where continued development could help you achieve greater emotional balance and interpersonal success.'
      );
      setTotalChartTitle('You show potential but could develop further');
      setTotalChartDescription(
        'Enhance emotional regulation and empathy to achieve greater balance and interpersonal success'
      );
      setNeurodivergentDescription(
        'You recognize that neurodivergent traits, such as those associated with ADHD, autism, or other cognitive differences, can influence how emotions are processed and expressed. You may sometimes feel uncertain about how to navigate these dynamics in yourself or others. Increasing your understanding of neurodivergent experiences and how they impact emotional regulation can foster more inclusive and supportive interactions.'
      );
      setMentalDescription(
        'You are aware that mental health conditions like anxiety, depression, or PTSD can affect emotional responses and self-regulation. There may be times when mental health challenges interfere with your emotional intelligence, making it harder to stay calm or connect with others. Acknowledging these influences without judgment and seeking appropriate support or coping strategies can help you maintain your emotional balance.'
      );
      setImpactDescription(
        'Past traumas may occasionally influence your current emotional reactions, sometimes leading to heightened sensitivity or difficulty in managing certain emotions. Triggers may arise unexpectedly, impacting your ability to remain composed. Developing trauma-informed coping mechanisms, such as grounding techniques or working with a therapist, can assist in managing these emotional responses more effectively.'
      );
      setGrowthArray([
        {
          title: 'Improving Emotional Regulation',
          description:
            'Focus on building a toolkit of techniques to manage your emotions, particularly during moments of stress or conflict. This could include journaling, mindfulness exercises, or learning to pause and reflect before responding emotionally.',
        },
        {
          title: 'Enhancing Empathy and Social Skills',
          description:
            "Actively practice empathy by putting yourself in others' shoes and listening without judgment. Engaging in activities that expose you to diverse perspectives can help broaden your understanding of emotional experiences different from your own.",
        },
        {
          title: 'Strengthening Communication and Conflict Resolution',
          description:
            'Improving your ability to express your emotions clearly and manage conflicts constructively can greatly enhance your relationship management skills. Consider learning assertive communication techniques or participating in workshops that focus on conflict resolution.',
        },
        {
          title: 'Fostering Resilience and Adaptability',
          description:
            'Develop routines and habits that support emotional resilience, such as setting achievable goals, maintaining a healthy lifestyle, and seeking support when needed. Building your adaptability skills can help you respond more positively to changes and setbacks.',
        },
      ]);
      setConclusionTitle('You have Moderate Emotional Intelligence');
      setConclusionDescription(
        'Your moderate level of emotional intelligence suggests that you are on a journey of emotional growth, with strengths in basic awareness and empathy. By focusing on enhancing your emotional regulation, communication skills, and resilience, you can build on your existing foundation and develop greater emotional insight and stability. Remember that emotional intelligence is a skill that can be nurtured, and with continued practice and self-compassion, you can achieve significant progress in your emotional well-being and interpersonal effectiveness.'
      );
    } else if (totalScore >= 80 && totalScore <= 119) {
      setTotalTitle('Low Emotional Intelligence');
      setTotalDescription(
        'Your score falls within the Low Emotional Intelligence category, indicating that while you have some awareness of emotions, you often face challenges in managing them effectively. You may find it difficult to regulate your emotional responses, connect deeply with others, or handle stress and conflict constructively. This profile acknowledges the difficulties you might experience with emotions and provides guidance on steps you can take to enhance your emotional intelligence over time.'
      );
      setTotalChartTitle('You face challenges but have room to grow');
      setTotalChartDescription(
        'Build foundational emotional skills to better handle stress, conflict, and relationships'
      );
      setNeurodivergentDescription(
        'Neurodivergent traits, such as those associated with ADHD, autism, or other cognitive differences, can significantly impact emotional processing and social interactions. You may find it particularly challenging to understand or regulate emotions due to sensory sensitivities, executive functioning issues, or social communication differences. Recognizing these factors as valid aspects of your experience is crucial, and seeking tailored strategies, such as structured routines or sensory-friendly environments, can be beneficial.'
      );
      setMentalDescription(
        'Mental health conditions like anxiety, depression, or trauma can greatly influence your emotional regulation. You might find yourself caught in cycles of negative thinking or emotional overwhelm, making it difficult to maintain composure or positive connections. Acknowledging the impact of mental health on your emotional intelligence and seeking professional support or therapeutic interventions can provide you with tools to better manage these challenges.'
      );
      setImpactDescription(
        'Mental health conditions like anxiety, depression, or trauma can greatly influence your emotional regulation. You might find yourself caught in cycles of negative thinking or emotional overwhelm, making it difficult to maintain composure or positive connections. Acknowledging the impact of mental health on your emotional intelligence and seeking professional support or therapeutic interventions can provide you with tools to better manage these challenges.'
      );
      setGrowthArray([
        {
          title: 'Building Emotional Awareness',
          description:
            'Start by taking small steps to identify your emotions, such as naming what you feel or keeping a journal of your daily emotional experiences. Understanding your emotional landscape is the first step in gaining control over how you respond.',
        },
        {
          title: 'Developing Basic Self-Regulation Skills',
          description:
            'Simple strategies like pausing before responding, engaging in physical activity, or practicing breathing exercises can help you begin to manage overwhelming emotions. Over time, these skills can help you feel more in control of your reactions.',
        },
        {
          title: 'Enhancing Empathy and Social Skills',
          description:
            'Practice being present in conversations and actively listening without interrupting. Showing genuine curiosity about others’ feelings and experiences can help you build stronger connections and develop a deeper understanding of social cues.',
        },
        {
          title: 'Strengthening Communication and Conflict Resolution',
          description:
            'Learning to express your feelings calmly and clearly, and to listen without immediately reacting defensively, can improve your ability to manage interpersonal conflicts. Consider seeking resources like communication workshops or support groups to practice these skills in a safe environment.',
        },
        {
          title: 'Building Resilience and Coping Strategies',
          description:
            'Focus on developing coping strategies that help you handle stress, such as setting achievable goals, breaking tasks into manageable steps, and celebrating small victories. Building resilience is a gradual process, and each small step forward is progress.',
        },
      ]);
      setConclusionTitle('You have Low Emotional Intelligence');
      setConclusionDescription(
        'Your score suggests that while emotional intelligence may not come naturally, it is an area where meaningful improvement is possible. By focusing on small, manageable steps towards greater emotional awareness, self-regulation, and social connection, you can enhance your emotional intelligence over time. Remember, emotional skills are learned and can be developed with patience, practice, and support. Embracing your journey with self-compassion and seeking resources tailored to your unique needs will empower you to make positive changes in your emotional well-being and relationships.'
      );
    } else if (totalScore >= 60 && totalScore <= 79) {
      setTotalTitle('Very Low Emotional Intelligence');
      setTotalDescription(
        'Your score indicates a Very Low level of Emotional Intelligence, suggesting that you experience significant challenges in understanding, managing, and expressing your emotions. You may frequently feel overwhelmed by your emotional reactions, struggle with empathy, and find it difficult to maintain positive relationships. While this score highlights areas of difficulty, it also provides an opportunity for personal growth. With targeted support and the right strategies, you can begin to build your emotional skills and improve your quality of life.'
      );
      setTotalChartTitle('You face significant challenges but growth is possible');
      setTotalChartDescription(
        'Start small by focusing on understanding and managing your emotions to enhance well-being and relationships'
      );
      setNeurodivergentDescription(
        'If you are neurodivergent, such as having ADHD, autism, or other cognitive differences, these traits can profoundly impact your emotional processing. Sensory sensitivities, challenges with executive function, or difficulties in social communication can all contribute to emotional dysregulation. Recognizing that these factors are part of your unique experience is essential, and seeking support or tailored strategies—like structured routines, visual aids, or therapy—can help manage these challenges.'
      );
      setMentalDescription(
        'Mental health conditions, such as anxiety, depression, PTSD, or other mood disorders, can significantly affect your emotional intelligence. These conditions can make emotional regulation and interpersonal interactions particularly challenging, often leading to emotional exhaustion, irritability, or withdrawal. Understanding that these difficulties are not a personal failing, but rather symptoms that can be managed with appropriate support, is key to moving forward.'
      );
      setImpactDescription(
        'Trauma can have a lasting impact on emotional regulation, making certain situations feel particularly triggering or overwhelming. Past traumatic experiences may cause you to react strongly in ways that feel uncontrollable or confusing. Seeking trauma-informed support, such as therapy or support groups, and learning grounding techniques can help you regain a sense of safety and control over your emotional responses.'
      );
      setGrowthArray([
        {
          title: 'Developing Emotional Awareness',
          description:
            'Start small by naming your emotions when you feel them—using simple labels like “angry,” “sad,” or “anxious” can help you begin to understand your emotional landscape. Keeping a journal of daily feelings or using apps designed to track mood can provide a clearer picture of your emotional patterns.',
        },
        {
          title: 'Practicing Basic Self-Regulation Skills',
          description:
            'Learn and practice basic techniques to calm yourself during emotional surges. This could include deep breathing exercises, short walks, listening to calming music, or using sensory tools like stress balls. These small interventions can help you start to gain some control over your emotional responses.',
        },
        {
          title: 'Improving Empathy and Connection',
          description:
            'Focus on improving your listening skills by paying full attention during conversations without interrupting or planning your response. Trying to reflect on what the other person might be feeling can gradually help you build empathy and improve your social awareness.',
        },
        {
          title: 'Strengthening Communication Skills',
          description:
            'Work on expressing your emotions in simple, clear terms. Avoid blaming language, and try to communicate how you feel without escalating conflict. Learning to express yourself calmly and listening actively to others can gradually improve your relationship skills.',
        },
        {
          title: 'Building Resilience Through Small Wins',
          description:
            'Set small, achievable goals that help you regain a sense of control and accomplishment. Celebrate even minor successes, as these can help build confidence and emotional resilience over time. Seeking support from friends, family, or professionals can also provide valuable encouragement.',
        },
      ]);
      setConclusionTitle('You have Very Low Emotional Intelligence');
      setConclusionDescription(
        'Your current score reflects significant challenges in emotional intelligence, but it’s important to remember that these skills can be developed with time, effort, and the right support. Focus on small, manageable steps to improve your emotional awareness, self-regulation, empathy, and communication skills. Emotional growth is a gradual process, and every positive change, no matter how small, is progress. Embrace this journey with patience, self-compassion, and a willingness to seek help when needed, as these efforts will lead to meaningful improvements in your emotional well-being and relationships.'
      );
    }
  }, [totalScore]);

  return (
    <div className="relative w-full overflow-x-hidden bg-white">
      <Image
        src="/images/emotional-intelligence/landing/gradient-desktop.svg"
        alt="Desktop Photos"
        width={1059}
        height={831}
        className="absolute left-[calc(50%-159px)] top-[0px] w-[1130px] h-auto hidden md:block z-10"
      />
      <div className="max-w-[1440px] mx-auto relative">
        <Image
          src="/images/emotional-intelligence/results/photos-desktop.png"
          alt="Desktop Photos"
          width={575}
          height={641}
          className="absolute right-[78.33px] top-[166px] w-[574px] h-auto md:h-auto rounded-[10px] hidden md:block z-10"
        />
      </div>
      <header className="max-w-[1440px] mx-auto flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px] z-20">
        <Logo />
        <Account />
      </header>
      <main>
        <HeaderContent totalScore={totalScore} totalTitle={totalTitle} totalDescription={totalDescription} />
        <div className="relative flex items-center justify-center md:hidden top-[-70px]">
          <Image
            src="/images/emotional-intelligence/results/result-photo-mobile.png"
            alt="Mobile photos"
            width={575}
            height={641}
            className="absolute w-[266px] h-auto z-10"
          />
          <Image
            src="/images/emotional-intelligence/results/result-gradient-mobile.png"
            alt="Mobile Gradient"
            width={375}
            height={448}
            className="w-full h-auto z-0 mt-[-36px]"
          />
        </div>
        <div className="flex flex-col gap-[28px] md:gap-[40px] max-w-[1288px] mx-auto px-4 md:px-0 py-[50px] md:py-[80px] mt-[-120px] md:mt-0">
          <div className="flex flex-col gap-3 w-full md:max-w-[634px] mx-auto md:text-center">
            <span className="font-ppmori font-semibold text-[28px] leading-[32px] tracking-[-0.03em] text-[#0C0113] md:text-[40px] md:leading-[50px] md:text-[#351C44]">
              Profile Break down
            </span>
            <span className="font-ppmori font-medium text-[16px] leading-[22px] text-[#8C8492] md:font-normal md:text-[18px] md:leading-[27px]">
              <span className="hidden md:block">
                Gain deeper insights into your emotional intelligence! Explore your scores across key areas
              </span>
              <span className="md:hidden">
                In-depth analysis on your Emotional Intelligence and what it means for you
              </span>
            </span>
          </div>
          <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-5">
            <CardWithGaugeChart
              alt="card 1"
              id="card_1"
              bg_source="/images/emotional-intelligence/results/card-1-bg.png"
              box_shadow="0px 4px 12px 0px #BF02EE3D"
              icon_1_source="/images/emotional-intelligence/results/icon-1-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-1-2.svg"
              header_text="Overall Profile"
              header_text_color="from-white to-[#F2BEFF]"
              chart_color_from="#ffffff"
              chart_color_to="#ffffff"
              chart_drop_shadow="#C7C7C759"
              remaining_color="#964DD5"
              bar_bg="#ffffff"
              bar_border="#AE54FF"
              value={totalScore}
              maxValue={300}
              value_color="from-white to-white"
              maxValue_color="#C7ADD9"
              title={totalChartTitle}
              title_color="#F7F7F7"
              description={totalChartDescription}
              description_color="#C7ADD9"
            />
            <CardWithGaugeChart
              alt="card 2"
              id="card_2"
              bg_source="/images/emotional-intelligence/results/card-normal-bg.png"
              box_shadow="0px 4px 12px 0px #54006914"
              icon_1_source="/images/emotional-intelligence/results/icon-2-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-2-2.svg"
              header_text="Emotional Self-Awareness"
              header_text_color="from-[#0C0113] to-[#1839AD]"
              chart_color_from="#7994F6"
              chart_color_to="#3658D0"
              chart_drop_shadow="#2245C259"
              remaining_color="#F5F5F5"
              bar_bg="#3F61D5"
              bar_border="#ffffff"
              value={emotionalScores['Emotional Self-Awareness']}
              maxValue={60}
              value_color="from-[#7994F6] to-[#3658D0]"
              maxValue_color="#8C8492"
              title={awarenessTitle}
              title_color="#0C0113"
              description={awarenessDescription}
              description_color="#8C8492"
            />
            <CardWithGaugeChart
              alt="card 3"
              id="card_3"
              bg_source="/images/emotional-intelligence/results/card-normal-bg.png"
              box_shadow="0px 4px 12px 0px #54006914"
              icon_1_source="/images/emotional-intelligence/results/icon-3-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-3-2.svg"
              header_text="Self-Regulation"
              header_text_color="from-[#0C0113] to-[#9C0C2E]"
              chart_color_from="#F47191"
              chart_color_to="#D93F64"
              chart_drop_shadow="#C2222273"
              remaining_color="#F5F5F5"
              bar_bg="#DF4A6E"
              bar_border="#ffffff"
              value={emotionalScores['Self-Regulation']}
              maxValue={60}
              value_color="from-[#D93F64] to-[#F47191]"
              maxValue_color="#8C8492"
              title={regulationTitle}
              title_color="#0C0113"
              description={regulationDescription}
              description_color="#8C8492"
            />
            <CardWithGaugeChart
              alt="card 4"
              id="card_4"
              bg_source="/images/emotional-intelligence/results/card-normal-bg.png"
              box_shadow="0px 4px 12px 0px #54006914"
              icon_1_source="/images/emotional-intelligence/results/icon-4-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-4-2.svg"
              header_text="Relationship Management"
              header_text_color="from-[#0C0113] to-[#757F00]"
              chart_color_from="#E8EB75"
              chart_color_to="#DFE33B"
              chart_drop_shadow="#C28C2273"
              remaining_color="#F5F5F5"
              bar_bg="#E3E645"
              bar_border="#ffffff"
              value={emotionalScores['Relationship Management']}
              maxValue={60}
              value_color="from-[#DFE33B] to-[#E6E965]"
              maxValue_color="#8C8492"
              title={relationshipTitle}
              title_color="#0C0113"
              description={relationshipDescription}
              description_color="#8C8492"
            />
            <CardWithGaugeChart
              alt="card 5"
              id="card_5"
              bg_source="/images/emotional-intelligence/results/card-normal-bg.png"
              box_shadow="0px 4px 12px 0px #54006914"
              icon_1_source="/images/emotional-intelligence/results/icon-5-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-5-2.svg"
              header_text="Empathy and Social Awareness"
              header_text_color="from-[#0C0113] to-[#057023]"
              chart_color_from="#A1F679"
              chart_color_to="#5ED036"
              chart_drop_shadow="#22C23B73"
              remaining_color="#F5F5F5"
              bar_bg="#68D640"
              bar_border="#ffffff"
              value={emotionalScores['Empathy and Social Awareness']}
              maxValue={60}
              value_color="from-[#8AE573] to-[#55D036]"
              maxValue_color="#8C8492"
              title={empathyTitle}
              title_color="#0C0113"
              description={empathyDescription}
              description_color="#8C8492"
            />
            <CardWithGaugeChart
              alt="card 6"
              id="card_6"
              bg_source="/images/emotional-intelligence/results/card-normal-bg.png"
              box_shadow="0px 4px 12px 0px #54006914"
              icon_1_source="/images/emotional-intelligence/results/icon-6-1.svg"
              icon_2_source="/images/emotional-intelligence/results/icon-6-2.svg"
              header_text="Adaptability and Resilience"
              header_text_color="from-[#0C0113] to-[#CF750D]"
              chart_color_from="#F4B368"
              chart_color_to="#EDA047"
              chart_drop_shadow="#9F2C0859"
              remaining_color="#F5F5F5"
              bar_bg="#EDA24C"
              bar_border="#ffffff"
              value={emotionalScores['Adaptability and Resilience']}
              maxValue={60}
              value_color="from-[#F4B368] to-[#EDA047]"
              maxValue_color="#8C8492"
              title={adaptabilityTitle}
              title_color="#0C0113"
              description={adaptabilityDescription}
              description_color="#8C8492"
            />
          </div>
        </div>
        <div className="w-full bg-[#FBF6FF]">
          <div className="w-full md:max-w-[852px] mx-auto px-4 md:px-0 py-[50px] md:py-[80px] flex flex-col justify-center gap-[28px] md:gap-[40px]">
            <h2 className="w-full md:max-w-[606px] font-ppmori font-semibold text-[#0C0113] text-[24px] md:text-[40px] leading-[32px] md:leading-[50px] tracking-[-0.03em] md:text-center mx-auto">
              Awareness of Neurodivergence and Mental Health Considerations
            </h2>
            <div className="flex flex-col gap-3 md:gap-[20px]">
              <BoxWithIcon
                source="/images/emotional-intelligence/results/sensitivities.svg"
                alt="sensitivities"
                heading="Neurodivergent Sensitivities"
                open={true}>
                {neurodivergentDescription}
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/results/awareness.svg"
                alt="awareness"
                heading="Mental Health Awareness"
                open={false}>
                {mentalDescription}
              </BoxWithIcon>
              <BoxWithIcon
                source="/images/emotional-intelligence/results/trauma.svg"
                alt="trauma"
                heading="Impact of Past Trauma"
                open={false}>
                {impactDescription}
              </BoxWithIcon>
            </div>
          </div>
        </div>
        <div className="flex md:flex flex-col gap-[28px] md:gap-[40px] md:max-w-[1288px] py-[50px] md:py-[80px] px-4 md:px-0 mx-auto">
          <h2 className="font-ppmori font-semibold text-[28px] leading-[32px] md:text-[40px] md:leading-[50px] tracking-[-0.03em] mx-auto">
            Areas for Continued Growth
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-5">
            {growthArray.map((item, index) => (
              <BoxWithNumber
                key={index}
                number={index + 1 < 10 ? `0${index + 1}` : index + 1}
                title={item.title}
                description={item.description}
              />
            ))}
          </div>
        </div>
        <div className="relative flex flex-col md:flex-row gap-[24px] md:gap-[96px] md:max-w-[1288px] mb-[50px] md:mb-[80px] mx-4 md:mx-auto p-[24px] md:p-[48px] rounded-[12px] bg-[#FBF6FF]">
          <Image
            src="/images/emotional-intelligence/results/conclusion-desktop.svg"
            alt="Desktop Conclusion"
            width={857}
            height={324}
            className="absolute hidden md:block w-auto h-full top-0 left-0"
          />
          <Image
            src="/images/emotional-intelligence/results/conclusion-mobile.svg"
            alt="Desktop Conclusion"
            width={857}
            height={324}
            className="absolute md:hidden w-full h-auto top-0 right-0"
          />
          <h2 className="w-full md:max-w-[396px] font-ppmori font-semibold text-[28px] leading-[32px] md:text-[40px] md:leading-[50px] tracking-[-0.03em] text-[#351C44]">
            Emotional Intelligence Conclusion
          </h2>
          <div className="w-full flex flex-col gap-3 md:gap-4 font-ppmori">
            <h3 className="font-semibold text-[18px] leading-[26px] md:text-[26px] md:leading-[30px] text-[#0C0113]">
              {conclusionTitle}
            </h3>
            <span className="font-normal text-[14px] leading-[20px] md:text-[18px] md:leading-[26px] text-[#8C8492]">
              {conclusionDescription}
            </span>
          </div>
        </div>
        <Footer />
      </main>
    </div>
  );
};

export default Results;
