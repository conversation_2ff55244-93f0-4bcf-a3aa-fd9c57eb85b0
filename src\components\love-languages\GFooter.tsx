import React from 'react';
import { useContext } from 'react';
import { Link } from '@/lib/i18n/navigation';
import { UserContext } from '@/store/UserContext';
import SessionContext from '@/store/SessionContext';

const Footer = () => {
  const { user } = useContext(UserContext);
  const { siteConfig } = useContext(SessionContext);
  return (
    <footer className="relative flex flex-wrap md:flex-nowrap justify-between items-center px-[16px] md:p-[34px_82px] text-sm md:text-base z-10">
      <div className="flex flex-wrap gap-x-[32px] gap-y-[20px]">
        <Link
          href="/"
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          Home
        </Link>
        <Link
          href={user ? `/love-languages/results` : `/love-languages/test`}
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          Results
        </Link>
        <Link
          href="https://iqinternational.org/insights/terms-and-conditions"
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          Terms & Conditions
        </Link>
        <Link
          href="/privacy"
          className="font-raleway font-semibold text-[16px] md:text-[18px] text-[#0E2432] leading-[20px] tracking-[-0.01em]">
          Privacy Policy
        </Link>
      </div>
      <div className="font-raleway font-semibold text-[18px] text-[#828E98] leading-[24px] md:leading-[20px] mt-4 md:mt-0 md:ml-auto mb-[23px] md:mb-0">
        Copyright © <span className="font-sans">{new Date().getFullYear()}</span> {siteConfig.siteName}
      </div>
    </footer>
  );
};

export default Footer;
