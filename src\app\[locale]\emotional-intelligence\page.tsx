'use client';
import Image from 'next/image';
import { Link } from '@/lib/i18n/navigation';
import { useContext } from 'react';
import { useRouter } from '@/lib/i18n/navigation';
import Logo from '@/components/emotional-intelligence/landing/Logo';
import IconWithDescription from '@/components/emotional-intelligence/landing/IconWithDescription';
import BoxWithIcon from '@/components/emotional-intelligence/landing/BoxWithIcon';
import LoginButton from '@/components/emotional-intelligence/landing/LoginButton';
import Footer from '@/components/emotional-intelligence/landing/GFooter';
import Account from '@/components/emotional-intelligence/Account';
import { UserContext } from '@/store/UserContext';

const LandingPage = () => {
  const { user } = useContext(UserContext);
  const router = useRouter(); // Initialize useRouter
  return (
    <div className="relative w-full overflow-x-hidden bg-white">
      <Image
        src="/images/emotional-intelligence/landing/gradient-desktop.svg"
        alt="Desktop Photos"
        width={1059}
        height={831}
        className="absolute left-[calc(50%-159px)] top-[0px] w-[1130px] h-auto hidden md:block z-10"
      />
      <div className="max-w-[1440px] mx-auto relative">
        <Image
          src="/images/emotional-intelligence/landing/photos-desktop.jpg"
          alt="Desktop Photos"
          width={575}
          height={641}
          className="absolute right-[78.33px] top-[166px] w-[574px] h-auto md:h-auto rounded-[10px] hidden md:block z-10"
        />
      </div>
      <header className="max-w-[1440px] mx-auto flex flex-row items-center justify-between px-[16px] py-[11px] md:px-[82px] md:py-[30px] z-20">
        <Logo />
        {user ? <Account /> : <LoginButton />}
      </header>
      <main className="max-w-[1440px] mx-auto">
        <div className="hero-section relative w-full m-0 p-0 bg-white pb-[50px] md:pb-[115px]">
          <div className="px-[16px] pt-[16px] md:pl-[82px] md:pt-[70px] md:max-w-[732px] relative z-20 mb-[24px] md:mb-[0px]">
            <label className="block text-[#0C0113] font-ppmori font-semibold tracking-[-0.05em] text-[36px] leading-[40px] md:mb-[42px] md:text-[64px] md:leading-[75px] mb-[16px] z-20">
              <span className="hidden md:block">
                Assess Your <br />
                Emotional Intelligence
              </span>
              <span className="md:hidden">Get Your EQ Score</span>
            </label>
            <label className="block md:max-w-[520px] text-[#8C8492] font-ppmori text-[16px] leading-[22px] md:text-[18px] md:leading-[27px] font-normal md:mr-[50px] mb-[24px]">
              <span className="hidden md:block">
                Unlock the key to better relationships, improved decision-making, and personal growth. Our EQ test helps
                you <span className="font-semibold text-[#0C0113]">understand</span> and{' '}
                <span className="font-semibold text-[#0C0113]">enhance</span> your{' '}
                <span className="font-semibold text-[#0C0113]">emotional awareness</span>, empowering you{' '}
                <span className="font-semibold text-[#0C0113]">to thrive in every aspect of life</span>.
              </span>
              <span className="md:hidden">
                Our EQ test helps you <span className="font-semibold text-[#0C0113]">understand</span> and{' '}
                <span className="font-semibold text-[#0C0113]">enhance</span> your{' '}
                <span className="font-semibold text-[#0C0113]">emotional awareness</span>, empowering you{' '}
                <span className="font-semibold text-[#0C0113]">to thrive in every aspect of life</span>
              </span>
            </label>
            <div className="flex flex-col md:max-w-[520px] gap-[16px] mb-[40px]">
              <IconWithDescription source="/images/emotional-intelligence/landing/gain.svg" alt="Gain Icon">
                <span className="font-semibold text-[#0C0113]">Gain insights</span> into how you process emotions and
                react to challenges
              </IconWithDescription>
              <IconWithDescription source="/images/emotional-intelligence/landing/strengthen.svg" alt="Strengthen Icon">
                <span className="font-semibold text-[#0C0113]">Strengthen your connections</span> with others by
                improving empathy and communication
              </IconWithDescription>
              <IconWithDescription source="/images/emotional-intelligence/landing/develop.svg" alt="Develop Icon">
                <span className="font-semibold text-[#0C0113]">Develop emotional resilience</span> to navigate
                life&apos;s ups and downs.
              </IconWithDescription>
            </div>
            <div className="relative w-full z-20 flex items-center">
              <div className="absolute inset-x-0 -bottom-[5px] w-full md:w-[300px] rounded-full bg-[#8C36D0] blur-[20px] opacity-100"></div>
              <Link
                href="/emotional-intelligence/test"
                className="font-ppmori font-semibold shadow-[0px_1px_1px_0px_#47007E] w-full md:w-[300px] h-[48px] md:h-[64px] bg-[#8C36D0] rounded-[10px] flex items-center justify-center px-[40px] py-[20px] tracking-[0.03em] text-white leading-[24px] text-[16px] md:text-[20px] relative z-10">
                <button>Take the Test</button>
              </Link>
            </div>
          </div>
          <div className="relative flex items-center justify-center md:hidden top-[-33px]">
            <Image
              src="/images/emotional-intelligence/landing/photos-mobile.svg"
              alt="Mobile photos"
              width={575}
              height={641}
              className="absolute w-[266px] h-auto z-10"
            />
            <Image
              src="/images/emotional-intelligence/landing/gradient-mobile.svg"
              alt="Mobile Gradient"
              width={375}
              height={448}
              className="w-full h-auto z-0 mt-[-36px]"
            />
          </div>
        </div>
        <div className="evolution-section relative w-full m-0 p-0 bg-white px-[16px] md:py-[80px] md:px-[82px] mb-[60px] md:mb-[0px] mt-[-76px] md:mt-0 bg-transparent">
          {/* Adjusted to force the image behind */}
          <div className="absolute inset-0 flex justify-center items-center">
            <Image
              src="/images/emotional-intelligence/landing/evolution-desktop.jpg"
              alt="Desktop Gradient"
              width={1144}
              height={877}
              className="hidden md:block w-[1144px] mt-[80px] ml-[120px] md:h-auto opacity-90"
              priority
            />
          </div>

          <label className="relative font-ppmori md:max-w-[620px] md:m-auto font-bold flex justify-center md:text-center text-[32px] leading-[36px] md:text-[56px] md:leading-[66px] tracking-[-0.03em] md:mb-[40px] mb-[26px] text-[#0C0113]">
            Understanding Emotional Intelligence
          </label>

          <div className="relative grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-5">
            <BoxWithIcon
              source="/images/emotional-intelligence/landing/emotional.svg"
              alt="Emotional Icon"
              heading="What is Emotional Intelligence?"
              open={true}>
              Emotional intelligence (EQ) is the ability to recognize, understand, and manage our own emotions
              effectively. It also involves empathizing with others, enabling us to navigate social situations, build
              meaningful relationships, and communicate more effectively
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/emotional-intelligence/landing/origins.svg"
              alt="Origins Icon"
              heading="The Origins"
              open={false}>
              The concept of EQ was first introduced by psychologists Peter Salovey and John D. Mayer in the early
              1990s. Daniel Goleman later popularized it with his book &quot;Emotional Intelligence&quot; in 1995,
              bringing widespread attention to its importance
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/emotional-intelligence/landing/advancements.svg"
              alt="Love Icon"
              heading="Advancements in Understanding"
              open={false}>
              Psychological research has expanded our understanding of EQ, highlighting key components like
              self-awareness, self-regulation, motivation, empathy, and social skills, which are essential for achieving
              personal and professional success
            </BoxWithIcon>
            <BoxWithIcon
              source="/images/emotional-intelligence/landing/modern.svg"
              alt="Modern Icon"
              heading="Modern Applications"
              open={false}>
              Emotional intelligence is now recognized as a critical skill in leadership, personal development, and
              interpersonal relationships. Ongoing research continues to explore its impact on mental health, workplace
              dynamics, and overall well-being
            </BoxWithIcon>
          </div>
        </div>
        <Footer />
        <div className="md:hidden w-full p-4 border border-[1px] border-[#E6E3E8] rounded-tl-[24px] rounded-tr-[24px] rounded-br-[0px] rounded-bl-[0px] bg-white">
          <button
            onClick={() => router.push('/emotional-intelligence/test')} // Redirect to checkout route
            className="w-full font-ppmori font-semibold text-[16px] py-[20px] px-[40px] rounded-[10px] bg-[#8C36D0] text-white leading-[24px]">
            Take the Test
          </button>
        </div>
      </main>
    </div>
  );
};

export default LandingPage;
