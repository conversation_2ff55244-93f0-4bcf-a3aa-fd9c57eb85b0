import Image from 'next/image';
import UpsalePath from '@/components/UpsaleFlow/UpselPath';
import { useState } from 'react';

export default function UpsaleThree({ handleNext }) {
  const [choose, setChoose] = useState(1);
  return (
    <div>
      <div className="container mx-auto">
        <UpsalePath />
        <div className="text-center text-[52px] text-[#191919] pt-[40px] md:pt-[60px]">
          <h1 className="text-[24px] md:text-[32px] pb-3 md:pb-3">Choose Your IQ Training Toolkit</h1>
          <div className="md:max-w-[200px] mx-auto flex bg-[#FFF4EA] p-1 justify-center rounded">
            <div>
              <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11 5.33181C11 6.33181 10.6667 7.66515 9.06667 8.19848C9.53333 7.06515 9.6 5.93182 9.26667 4.86515C8.8 3.46515 7.26667 2.39848 6.2 1.79848C5.93333 1.59848 5.46667 1.86515 5.53333 2.26515C5.53333 2.99848 5.33333 4.06515 4.2 5.19848C2.73333 6.66515 2 8.19848 2 9.66515C2 11.5985 3.33333 13.9985 6 13.9985C3.33333 11.3318 5.33333 8.99848 5.33333 8.99848C5.86667 12.9318 8.66667 13.9985 10 13.9985C11.1333 13.9985 13.3333 13.1985 13.3333 9.73182C13.3333 7.66515 12.4667 6.06515 11.7333 5.13181C11.5333 4.79848 11.0667 4.99848 11 5.33181Z"
                  class="fill-primary"
                />
              </svg>
            </div>
            <p className="pl-1 text-primary text-[16px]">Available Only Now</p>
          </div>
          <h3 className="pt-4 text-[16px] md:text-[20px]">Select from these exclusive, limited-time tools:</h3>
        </div>
        <div className="justify-between max-w-[840px] mx-auto mb-3">
          <div
            onClick={() => {
              setChoose(1);
            }}
            className={`md:w-[630px] p-1 bg-white border shadow mt-3 md:mt-2 mx-auto flex rounded ${
              choose == 1 && ' border-orange-300'
            }  cursor-pointer`}>
            <div className="w-[30px] h-[30px] ml-1 md:ml-0 mt-1 mr:mr-0 md:mr-4">
              <div
                className={`w-[26px] h-[26px] border rounded-full mr-5 ${
                  choose == 1 && 'border-8 border-orange-400'
                }`}></div>
            </div>
            <div className="flex justify-between w-full">
              <div className="w-full">
                <div className="flex justify-between w-full md:p-1">
                  <div>
                    <h4 className="text-[15px] md:text-[16px] leading-3">All-in-One Bundle</h4>
                    <p className="text-[12px] md:text-[14px]">Complete package to boost your potential</p>
                    <div className="flex">
                      <h2 className="text-[16px] md:text-[20px] mt-0 md:mt-5 mr-2">$34.99 </h2>
                      <b className="text-[#8893AC] text-[12px] md:text-[15px] mt-0 md:mt-6">
                        $<span className="line-through">99.97</span>
                      </b>
                      <div className="ml-1 md:ml-8 flex bg-[#FFF4EA] md:p-1 mt-0 md:mt-5 rounded">
                        <p className="pl-1 text-primary text-[11px] md:text-[16px] font-bold">Save 65%</p>
                      </div>
                    </div>
                  </div>
                  <img
                    src="/images/upsell/all-in-one.svg"
                    className="block ml-auto w-[45px] h-[58px]  md:w-[69.25px] md:h-[75.63px]"
                  />
                </div>
                <div className="border-t md:mt-4">
                  <div>
                    <p className="font-bold hidden md:block text-[#191919] text-[12px] md:text-[16px] md:pt-1 md:pt-3 md:pb-1">
                      Includes:
                    </p>
                    <div className="md:flex">
                      <div className="flex md:py-1">
                        {' '}
                        <svg
                          width="20"
                          height="20"
                          className="w-[12px] h-[12px] md:w-[20px] md:h-[20px]"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <circle cx="10" cy="10" r="10" class="fill-primary" />
                          <path
                            d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                            fill="white"
                          />
                        </svg>
                        <p className="ml-2 text-[10px] md:text-[14px]">Career & education guidance</p>
                      </div>{' '}
                      <div className="flex md:py-1 md:ml-4">
                        {' '}
                        <svg
                          width="20"
                          height="20"
                          className="w-[12px] h-[12px] md:w-[20px] md:h-[20px]"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <circle cx="10" cy="10" r="10" class="fill-primary" />
                          <path
                            d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                            fill="white"
                          />
                        </svg>
                        <p className="ml-2 text-[10px] md:text-[14px]">Stress management secrets</p>
                      </div>
                    </div>
                    <div className="flex md:py-1">
                      {' '}
                      <svg
                        width="20"
                        height="20"
                        className="w-[12px] h-[12px] md:w-[20px] md:h-[20px]"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="10" class="fill-primary" />
                        <path
                          d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                          fill="white"
                        />
                      </svg>
                      <p className="ml-2 text-[10px] md:text-[14px]">Brain biohacks toolkit</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            onClick={() => {
              setChoose(2);
            }}
            className={`md:w-[630px] p-1 bg-white border shadow mt-3 md:mt-2 mx-auto flex rounded ${
              choose == 2 && ' border-orange-300'
            }  cursor-pointer`}>
            <div className="w-[30px] h-[30px] ml-1 md:ml-0 mt-1 mr:mr-0 md:mr-4">
              <div
                className={`w-[26px] h-[26px] border rounded-full mr-5 ${
                  choose == 2 && 'border-8 border-orange-400'
                }`}></div>
            </div>
            <div className="flex justify-between w-full">
              <div className="w-full">
                <div className="flex justify-between w-full">
                  <div>
                    <h4 className="text-[15px] md:text-[17px] leading-4">Career & Education IQ Guide</h4>
                    <p className="text-[12px] md:text-[14px]">Your roadmap to success in career and learning</p>
                    <div className="flex">
                      <h2 className="text-[16px] md:text-[20px] mt-0 md:mt-5 mr-2">$18.99 </h2>
                      <b className="text-[#8893AC] text-[12px] md:text-[15px] mt-0 md:mt-6">
                        $<span className="line-through">28.99</span>
                      </b>
                      <div className="ml-1 md:ml-8 flex bg-[#FFF4EA] p-1 md:mt-5 rounded">
                        <p className="pl-1 text-primary text-[11px] md:text-[16px] font-bold">Save 33%</p>
                      </div>
                    </div>
                  </div>
                  <img
                    src="/images/upsell/iq-guide.svg"
                    className="block ml-auto w-[47px] h-[48px] md:w-[73.41px] md:h-[75.63px]"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            onClick={() => {
              setChoose(3);
            }}
            className={`md:w-[630px] p-1 bg-white border shadow mt-3 md:mt-2 mx-auto flex rounded ${
              choose == 3 && ' border-orange-300'
            }  cursor-pointer`}>
            <div className="w-[30px] h-[30px] ml-1 md:ml-0 mt-1 mr:mr-0 md:mr-4">
              <div
                className={`w-[26px] h-[26px] border rounded-full mr-5 ${
                  choose == 3 && 'border-8 border-orange-400'
                }`}></div>
            </div>
            <div className="flex justify-between w-full">
              <div className="w-full">
                <div className="flex justify-between w-full">
                  <div>
                    <h4 className="text-[15px] md:text-[17px] leading-4">Stress Management Secrets</h4>
                    <p className="text-[12px] md:text-[14px]">Proven methods for a calm, focused mind</p>
                    <div className="flex">
                      <h2 className="text-[16px] md:text-[20px] mt-0 md:mt-5 mr-2">$14.99</h2>
                      <b className="text-[#8893AC] text-[12px] md:text-[15px] mt-0 md:mt-6">
                        $<span className="line-through">29.99</span>
                      </b>
                      <div className="ml-1 md:ml-8 flex bg-[#FFF4EA] p-1 md:mt-5 rounded">
                        <p className="pl-1 text-primary text-[11px] md:text-[16px] font-bold">Save 50%</p>
                      </div>
                    </div>
                  </div>
                  <img
                    src="/images/upsell/stress-secrets.svg"
                    className="block ml-auto w-[51px] h-[51px] md:w-[82px] md:h-[82px]"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            onClick={() => {
              setChoose(4);
            }}
            className={`md:w-[630px] p-1 bg-white border shadow mt-3 md:mt-2 mx-auto flex rounded ${
              choose == 4 && ' border-orange-300'
            }  cursor-pointer`}>
            <div className="w-[30px] h-[30px] ml-1 md:ml-0 mt-1 mr:mr-0 md:mr-4">
              <div
                className={`w-[26px] h-[26px] border rounded-full mr-5 ${
                  choose == 4 && 'border-8 border-orange-400'
                }`}></div>
            </div>
            <div className="flex justify-between w-full">
              <div className="w-full">
                <div className="flex justify-between w-full">
                  <div>
                    <h4 className="text-[15px] md:text-[17px] leading-4">Brain Biohacks Toolkit</h4>
                    <p className="text-[12px] md:text-[14px]">Boost brainpower with cutting-edge techniques</p>
                    <div className="flex">
                      <h2 className="text-[16px] md:text-[20px] mt-0 md:mt-5 mr-2">$19.99</h2>
                      <b className="text-[#8893AC] text-[12px] md:text-[15px] mt-0 md:mt-6">
                        $<span className="line-through">28.99</span>
                      </b>
                      <div className="ml-1 md:ml-8 flex bg-[#FFF4EA] p-1 md:mt-5 rounded">
                        <p className="pl-1 text-primary text-[11px] md:text-[16px] font-bold">Save 29%</p>
                      </div>
                    </div>
                  </div>
                  <img
                    src="/images/upsell/brain-toolkit.svg"
                    className="block ml-auto w-[48px] h-[48px] md:w-[75.62px] md:h-[75.62px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex border-t-2 justify-between">
          <button
            onClick={() => {
              handleNext();
            }}
            className="px-8 w-[32%] mx-[1%] md:w-auto py-3 bg-[#E8EDF8] text-black rounded-md md:my-6">
            Skip
          </button>
          <button
            onClick={() => {
              handleNext();
            }}
            className="text-white w-[66%] mx-[1%] md:w-[300px] px-8 py-3 bg-primary rounded-md md:my-6">
            Claim My Offer
          </button>
        </div>
      </div>
    </div>
  );
}
