import { usePostHog } from 'posthog-js/react';
import { PostHogEventEnum } from '@/store/types';

interface EventProperties {
  [key: string]: any;
}

export const usePostHogAnalytics = () => {
  const posthog = usePostHog();
  const captureEvent = (eventName: PostHogEventEnum, properties: EventProperties) => {
    if (posthog) posthog.capture(eventName, properties);
  };

  const identifyUser = (distinctId: string, properties: EventProperties) => {
    if (posthog) posthog.identify(distinctId, properties);
  };

  return { captureEvent, identifyUser };
};
