'use client';

import { useSearchParams } from 'next/navigation';
import { useCallback, useContext, useEffect, useRef } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { usePathname } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const { paymentSystem, updatePaymentSystem } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const hasMounted = useRef(false);
  const pathname = usePathname();
  const isSpecialRoute = pathname.startsWith('/love-languages') || pathname.startsWith('/emotional-intelligence');

  // Use callback to prevent re-rendering
  const setPaymentSystem = useCallback(() => {
    const paymentParam = searchParams.get('payment');

    if (!paymentSystem && process.env.NEXT_PUBLIC_USE_PAYMENT) {
      updatePaymentSystem(process.env.NEXT_PUBLIC_USE_PAYMENT);
    }

    if (paymentParam === 'solidgate' || paymentParam === 'stripe') {
      updatePaymentSystem(paymentParam);
    }
  }, [searchParams, paymentSystem, updatePaymentSystem]);

  useEffect(() => {
    // Ensure setPaymentSystem is called once after mount
    if (!hasMounted.current) {
      hasMounted.current = true;
      setPaymentSystem();
    }
  }, [setPaymentSystem]);

  return isSpecialRoute ? (
    <>{children}</> // No layout
  ) : (
    <div className="flex flex-col min-h-lvh justify-between">
      <Header />
      <div className="grow flex justify-center">{children}</div>
      <Footer />
    </div>
  );
}
