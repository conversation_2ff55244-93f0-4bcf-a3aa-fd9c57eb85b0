'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

import CheckoutV1 from '@/components/CheckoutV1/CheckoutV1';
import CheckoutV2 from '@/components/CheckoutV2/CheckoutV2';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

const Checkout = () => {
  const t = useTranslations('checkout');
  const { captureEvent } = usePostHogAnalytics();
  const [version, setVersion] = useState<'v1' | 'v2' | null>(null);

  useEffect(() => {
    const checkoutVersion = localStorage.getItem('checkoutVersion') || 'v1';
    setVersion(checkoutVersion as 'v1' | 'v2');
  }, []);

  useEffect(() => {
    captureEvent(PostHogEventEnum.CHECKOUT_PAGE_VIEWED, {});
    // eslint-disable-next-line
  }, []);

  if (version === 'v1') {
    return <CheckoutV1 />;
  } else if (version === 'v2') {
    return <CheckoutV2 />;
  } else {
    return <div>{t('loading')}</div>;
  }
};
export default Checkout;
