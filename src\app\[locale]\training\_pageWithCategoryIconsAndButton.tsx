'use client';

import { useMemo } from 'react';
import dynamic from 'next/dynamic';
import { Link } from '@/lib/i18n/navigation';
import ResultsButtons from '@/components/ClientComponents/ResultsButtons';

const DynamicResultsButtons = dynamic(() => import('@/components/ClientComponents/ResultsButtons'), {
  ssr: false,
  loading: () => <ResultsButtons {...{ sessionIdToUse: 'example', loading: true }} />,
});

const Trainings = () => {
  const categories = useMemo(() => ['analytical', 'pattern', 'visual'], []);

  return (
    <ul>
      {categories.map((category) => (
        <li key={category}>
          <Link href={`/${category}`}></Link>
        </li>
      ))}
    </ul>
  );
};

export default Trainings;
