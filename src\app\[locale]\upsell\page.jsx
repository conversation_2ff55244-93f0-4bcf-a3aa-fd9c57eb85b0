'use client';

import React, { useState } from 'react';
import UpsaleOne from '@/components/UpsaleFlow/UpsaleOne';
import UpsaleTwo from '@/components/UpsaleFlow/UpsaleTwo';
import UpsaleThree from '@/components/UpsaleFlow/UpsaleThree';
import UpsaleHeader from '@/components/UpsaleFlow/UpsaleHeader';
import UpsaleFour from '@/components/UpsaleFlow/UpsaleFour';
import UpsaleFive from '@/components/UpsaleFlow/UpsaleFive';

export default function Upsell() {
  const handleNext = () => {
    if (step < components.length - 1) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 0) setStep(step - 1);
  };

  const [step, setStep] = useState(0);

  const components = [
    <UpsaleOne key={0} handleNext={handleNext} />,
    <UpsaleTwo key={1} handleNext={handleNext} />,
    <UpsaleThree key={2} handleNext={handleNext} />,
    <UpsaleFour key={3} handleNext={handleNext} />,
    <UpsaleFive key={4} handleNext={handleNext} />,
  ];

  return (
    <div className="bg-[#F9FBFF] w-full">
      <div className="w-full md:w-[1200px] mx-auto">
        <UpsaleHeader handleBack={handleBack} />
        <div>
          <div className="text-left">{components[step]}</div>
        </div>
      </div>
    </div>
  );
}
