'use client';

import { memo } from 'react';
import { usePathname } from '@/lib/i18n/navigation';
import Copyright from '@/components/Footer/Copyright';
import PaymentMethods from '@/components/Footer/PaymentMethods';
import LegalMenu from '@/components/Footer/LegalMenu';
import MainMenu from '@/components/Footer/MainMenu';
import SupportSection from '@/components/Footer/SupportSection';
import FooterLogo from '@/components/Footer/FooterLogo';

// Pages where footer should be hidden
const FOOTER_HIDDEN_PAGES: readonly string[] = [
  '/form',
  '/calculation',
  '/checkout',
  '/checkout-v2',
  '/checkout-v2/upsell',
  '/mobile-checkout',
  '/questions',
];

const Footer = () => {
  const activePath = usePathname();
  const shouldShowFooter = !FOOTER_HIDDEN_PAGES.includes(activePath);

  return (
    <>
      {shouldShowFooter && (
        <div className="w-full font-segoe">
          <div className="flex flex-col max-w-[1280px] mx-[16px] my-[24px] md:mx-auto md:my-[48px]">
            <div className="flex flex-col md:flex-row w-full gap-[24px] md:justify-between border-b border-gray-500 pb-[24px]">
              <FooterLogo />
              <div className="flex flex-col md:flex-row gap-[16px] md:gap-[100px] ">
                <SupportSection />
                <div className="flex flex-col md:flex-row gap-[16px] md:gap-[80px] font-semibold tracking-[0]">
                  <LegalMenu />
                  <MainMenu />
                </div>
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-[16px] md:gap-[49px] py-0 md:py-[4px] mt-[24px]">
              <Copyright />
              <PaymentMethods />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Footer);
