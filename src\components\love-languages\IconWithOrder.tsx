import Image from 'next/image';
import React, { ReactNode } from 'react';

type IconWithOrderProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithOrder: React.FC<IconWithOrderProps> = ({ source, alt, children }) => {
  return (
    <div className="bg-[#F2FAFF] rounded-[12px] p-3 md:p-6">
      <div className="flex flex-row items-center gap-3 md:gap-4">
        <Image src={source} alt={alt} width={48} height={48} className="w-[40px] h-[40px] md:w-[48px] md:h-[48px]" />
        <span className="font-raleway font-semibold text-[14px] md:text-[20px] leading-[18px] md:leading-[26px] text-[#0E2432]">
          {children}
        </span>
      </div>
    </div>
  );
};

export default IconWithOrder;
