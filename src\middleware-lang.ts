import { match } from '@formatjs/intl-localematcher';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Negotiator from 'negotiator';
import { defaultLocale, defaultCountry, locales as availableLocales } from '@/lib/i18n/locales';

/* -------------------------------------------------------
* Middleware
* ----------------------------------------------------- */
export async function middleware(req: NextRequest) {
  const response = NextResponse.next();

  /* -------- 1.  Country-level locale cookie -------- */
  const locale   = req.geo?.country ?? defaultCountry;
  response.cookies.set('locale', locale);

  /* -------- 2.  Language already in pathname? -------- */
  const { pathname } = req.nextUrl;

  /* ② verify that the segment is an allowed locale */
  const pathLang = availableLocales.find(
    lang => pathname === `/${lang}` || pathname.startsWith(`/${lang}/`)
  );

  if (pathLang) {
    return response;
  }

  /* -------- 3.  Detect language from Accept-Language -------- */
  const acceptedLanguages = new Negotiator({ headers: req.headers as any }).languages();
  const language = match(acceptedLanguages, availableLocales, defaultLocale);

  /* -------- 4.  Redirect to language-prefixed URL -------- */
  req.nextUrl.pathname = `/${language}${pathname}`;
  return NextResponse.redirect(req.nextUrl);
}

/* -------------------------------------------------------
* Matcher
* ----------------------------------------------------- */
export const config = {
  matcher: [
    // Skip internal & static files
    '/((?!api|assets|.*\\..*|_next).*)',
  ],
};