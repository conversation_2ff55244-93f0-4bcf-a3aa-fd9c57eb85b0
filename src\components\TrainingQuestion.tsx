"use client";

import React, { useContext, useEffect } from "react";
import { useState } from "react";
import TrainingQuestionsImage from "@/components/ClientComponents/TrainingQuestionsImage";
import TrainingAnswerCard from "@/components/MemberArea/Cards/TrainingAnswerCard";
import NotDonePopUp from "@/components/PopUps/QuestionsPage/NotDonePopUp";
import { UserContext } from "@/store/UserContext";

export default function TrainingQuestion({ category, questionId, back, next, selectAnswer }: { category: string; questionId: number, back: () => void, next: () => void, selectAnswer: (answer_id: number) => void }) {  
  const [visible, setVisible] = useState<boolean>(false);
  const [selected, setSelected] = useState<number | null>(null);
  const { trainingQuestions: questions } = useContext(UserContext);
  const question = questions[category].find((q) => q.id == questionId)!;

  useEffect(() => {
    setSelected(null);
  }, [questionId]);

  return (
    <div style={{ padding: "0px 5.69444%" }} className="flex flex-wrap lg:px-[5.69444%] mt-[50px] 2xl:mt-[75px] mb-10">
      <div className="flex flex-wrap justify-between m-auto mt-1 xs:mt-3 max-w-screen-xl">
        <div className="flex flex-wrap justify-center m-auto gap-10 max-w-[950px]">
          <div className="flex flex-col w-full md:w-[45%] gap-5">
            <h1 className="text-[20px] tracking-normal my-2 xs:my-4">
              Question {questionId}/{questions[category].length}
            </h1>
            <TrainingQuestionsImage questionId={question.originalQuestionId} />
          </div>
          <div className="flex flex-col w-full md:w-[45%] gap-5">
            <h5 className="w-full my-2 xs:my-4" style={{ fontSize: 20, lineHeight: " 130%", letterSpacing: 0 }}>
              {question.originalQuestionId !== 27 ? "Select an answer" : `Select the mirror image`}
            </h5>
            <div className="flex flex-wrap gap-2 basis-full items-end justify-start md:justify-center">
              {Array(question?.numberOfAnswers)
                .fill(1)
                .map((_, i) => (
                  <div key={`${question.id}-${i}`} className="w-[30%] min-w-[0px]" onClick={() => {setSelected(i + 1); selectAnswer(i+1);}}>
                    <TrainingAnswerCard key={questionId} question={question} answerId={i + 1} selected={selected == i + 1} />
                  </div>
                ))}
            </div>
          </div>
          <div className={`w-full flex flex-wrap flex-row justify-between px-2`}>
            {!!selected && <div className="text-green-600 font-semibold w-full text-center mb-5">{question.explanation}</div>}
            <button
              onClick={back}
              className={`button primaryColor font-semibold py-3 px-[18px] md:w-[150px] w-[120px] border border-primary text-white bg-primary`}
              style={{
                borderRadius: 10,
                lineHeight: "120%",
              }}
            >
              Back
            </button>
            <button
              onClick={next}
              className={`button primaryColor font-semibold  py-4 px-[18px] md:w-[150px] w-[120px] border border-primary text-white bg-primary`}
              style={{
                borderRadius: 10,
                lineHeight: "120%",
              }}
            >
              {questionId == questions[category].length ? "Finish" : "Next"}
            </button>
          </div>
        </div>
      </div>
      {visible && (
        <div>
          <NotDonePopUp {...{ visible, setVisible }} />
        </div>
      )}
    </div>
  );
}
