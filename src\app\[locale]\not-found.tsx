"use client";

import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import MobileMenu from '@/components/MobileMenu';
import { Link } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';

export default function NotFound() {
  const t = useTranslations('not_found');
  const { siteConfig } = useContext(SessionContext);
  return (
    <div style={{ overflow: 'hidden' }}>
      <div className='grid place-content-center h-screen'>
        <div className='container mx-auto max-w-md text-center'>
          <h1 className='text-4xl my-4'>{t('title')}</h1>
          <p className=''>
            {t('description')}
          </p>
          <div className='border border-b-gray-50 my-4'></div>
          <div className='flex flex-col'>
            <Link className='hover:underline text-primary mb-4' href='/'>
              {t('btn_go_home')}
            </Link>
            {siteConfig.insights?.show &&
              <Link className='hover:underline text-primary mb-4' href='/insights' prefetch={false}>
                {t('btn_read_insights')}
              </Link>
            }
          </div>
        </div>
      </div>
      <MobileMenu />
    </div>
  );
}
