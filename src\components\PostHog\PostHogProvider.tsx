'use client';

import { useEffect } from 'react';
import posthog from 'posthog-js';
import { PostHogProvider as PHProvider } from 'posthog-js/react';
import { SuspendedPostHogPageView } from './page-view';

const key = process.env.NEXT_PUBLIC_POSTHOG_API_KEY;
const host = process.env.NEXT_PUBLIC_POSTHOG_HOST;

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    posthog.init(key as string, {
      api_host: host,
      capture_pageview: false, // we have our own page for tracking pageviews
      capture_pageleave: true,
    });
  }, []);

  if (!key || !host) {
    return <>{children}</>;
  }

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}
