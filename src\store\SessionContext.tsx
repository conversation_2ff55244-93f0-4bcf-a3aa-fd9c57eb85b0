'use client';
import { Prices } from '@/app/prices';
import { InitConfig } from '@solidgate/react-sdk';
import Cookies from 'js-cookie';
import lscache from 'lscache';
import { createContext, useCallback, useEffect, useState, ReactNode } from 'react';
import { v4 as uuid } from 'uuid';
import { getSiteConfig } from '../../site.config';
import type { Answer, FormData, SessionContextProps, SessionData } from './types';
import { update } from './utils';

const SessionContext = createContext<SessionContextProps>({
  sessionId: '',
  locale: '',
  questionId: 1,
  answers: [],
  formData: { name: '', email: '' },
  checkoutId: '',
  paymentStatus: '',
  getIsTrial: () => true,
  prices: {
    country: '',
    currency: '',
    symbol: '',
    subscription: { amount: 0, formatted: '' },
    oneTime: { amount: 0, formatted: '' },
    vatIncluded: false,
  },
  stripeInvoices: { data: [] },
  solidgateInvoices: { data: [] },
  siteConfig: getSiteConfig(),
  merchantData: null,
  results: [],
  emotionalScores: {},
  stage: 1, // Added for test progress
  clickedButton: null, // Added for selected answer
  testAnswers: [], // Added for test answers
  emotionalStage: 1, // Added for emotional intelligence test progress
  emotionalClickedButton: null, // Added for selected answer in emotional intelligence test
  emotionalTestAnswers: [], // Added for emotional intelligence test answers
  updateSessionId: async (newSessionId: string) => {},
  updateLocale: async (newLocale: string) => {},
  updateQuestionId: async (newQuestionId: number) => {},
  updateAnswer: async ({ questionId, answerId }: { questionId: number; answerId: number }) => {},
  updateAllAnswers: async (answers: Answer[]) => {},
  updateFormData: async (formData: FormData) => {},
  updateCheckoutId: async (newCheckoutId: string) => {},
  updatePaymentStatus: async (newPaymentStatus: string) => {},
  paymentSystem: null,
  fetchAllSessionData: () => ({
    sessionId: '',
    locale: '',
    questionId: 1,
    answers: [],
    formData: { email: '', name: '' },
    checkoutId: '',
    paymentStatus: '',
    stage: 1, // Added for test progress
    clickedButton: null, // Added for selected answer
    testAnswers: [], // Added for test answers
    emotionalStage: 1, // Added for emotional intelligence test progress
    emotionalClickedButton: null, // Added for selected answer in emotional intelligence test
    emotionalTestAnswers: [], // Added for emotional intelligence test answers
  }),
  updateAllSessionData: async (data: SessionData) => {},
  resetAnswers: async () => {},
  updateStripeInvoices: async () => {},
  updateSolidgateInvoices: async () => {},
  updateResults: async () => {},
  updatePaymentSystem: (system: string) => {},
  updateEmotionalScores: async () => {},
  updateStage: async (newStage: number) => {}, // Added for test progress
  updateClickedButton: async (newClickedButton: string | null) => {}, // Added for selected answer
  updateTestAnswers: async (newTestAnswers: string[]) => {}, // Added for test answers
  resetTestState: async () => {}, // Added to reset test-related state
  updateEmotionalStage: async () => {}, // Added for emotional intelligence test progress
  updateEmotionalClickedButton: async () => {}, // Added for selected answer in emotional intelligence test
  updateEmotionalTestAnswers: async () => {}, // Added for emotional intelligence test answers
  resetEmotionalTestState: async () => {}, // Added to reset emotional intelligence test state,
  updateMerchantData: async () => {},
});

export const SessionProvider: any = ({
  children,
  prices: pricesProp,
  siteConfig: siteConfigProp,
}: {
  children: ReactNode;
  prices: Prices;
  siteConfig: ReturnType<typeof getSiteConfig>;
}) => {
  const [sessionId, setSessionId] = useState<string>(lscache.get('se-sessionId') || '');
  const [locale, setLocale] = useState<string>(lscache.get('se-locale') || '');
  const [questionId, setQuestionId] = useState<number>(1);
  const [answers, setAnswers] = useState<Answer[]>(lscache.get('se-answers') || []);
  const [formData, setFormData] = useState<FormData>(lscache.get('se-formData') || { email: '' });
  const [checkoutId, setCheckoutId] = useState<string>(lscache.get('se-checkoutId') || '');
  const [paymentStatus, setPaymentStatus] = useState<string>(lscache.get('se-paymentStatus') || '');
  const [prices, setPrices] = useState<Prices>(pricesProp);
  const [siteConfig, setSiteConfig] = useState(siteConfigProp);
  const [paymentSystem, setPaymentSystem] = useState(lscache.get('se-paymentSystem') || null);
  const [results, setResults] = useState<any>(lscache.get('se-results') || []);
  const [emotionalScores, setEmotionalScores] = useState<any>(lscache.get('se-emotionalScores') || {});
  const [stage, setStage] = useState<number>(lscache.get('se-stage') || 1); // Added for test progress
  const [clickedButton, setClickedButton] = useState<string | null>(lscache.get('se-clickedButton') || null); // Added for selected answer
  const [testAnswers, setTestAnswers] = useState<string[]>(lscache.get('se-testAnswers') || []); // Added for test answers
  const [emotionalStage, setEmotionalStage] = useState<number>(lscache.get('se-emotionalStage') || 1);
  const [stripeInvoices, setStripeInvoices] = useState<{ data: any[] }>(
    lscache.get('se-stripeInvoices') || { data: [] }
  );
  const [solidgateInvoices, setSolidgateInvoices] = useState<{ data: any[] }>(
    lscache.get('se-solidgateInvoices') || { data: [] }
  );
  const [emotionalClickedButton, setEmotionalClickedButton] = useState<string | null>(
    lscache.get('se-emotionalClickedButton') || null
  );
  const [merchantData, setMerchantData] = useState<InitConfig['merchantData'] | null>(
    lscache.get('se-merchantData') || null
  );
  const [emotionalTestAnswers, setEmotionalTestAnswers] = useState<number[]>(
    lscache.get('se-emotionalTestAnswers') || []
  );

  const getIsTrial = useCallback(() => {
    if (typeof window !== 'undefined') {
      const country = Cookies.get('locale') || '';

      return country !== 'AE';
    }
    return true;
  }, []);

  const createSession = useCallback((customSessionId?: string) => {
    const newSessionId = customSessionId || uuid();
    setSessionId(newSessionId);
    lscache.set('se-sessionId', newSessionId, 525601);
  }, []);

  useEffect(() => {
    const sessionIdFromLocalStorage = lscache.get('se-sessionId');
    if (sessionIdFromLocalStorage !== null) {
      hydrateFromLocalStorage();
    } else {
      createSession();
    }
  }, [createSession]);

  const hydrateFromLocalStorage = () => {
    const sessionIdFromLocalStorage = lscache.get('se-sessionId');
    const questionIdFromLocalStorage = lscache.get('se-questionId');
    const answersFromLocalStorage = lscache.get('se-answers');
    const formDataFromLocalStorage = lscache.get('se-formData');
    const checkoutIdFromLocalStorage = lscache.get('se-checkoutId');
    const paymentStatusFromLocalStorage = lscache.get('se-paymentStatus');
    const stageFromLocalStorage = lscache.get('se-stage');
    const clickedButtonFromLocalStorage = lscache.get('se-clickedButton');
    const testAnswersFromLocalStorage = lscache.get('se-testAnswers');

    sessionIdFromLocalStorage && setSessionId(sessionIdFromLocalStorage);
    questionIdFromLocalStorage && setQuestionId(questionIdFromLocalStorage);
    answersFromLocalStorage && setAnswers(answersFromLocalStorage);
    formDataFromLocalStorage && setFormData(formDataFromLocalStorage);
    checkoutIdFromLocalStorage && setCheckoutId(checkoutIdFromLocalStorage);
    paymentStatusFromLocalStorage && setPaymentStatus(paymentStatusFromLocalStorage);
    stageFromLocalStorage && setStage(stageFromLocalStorage);
    clickedButtonFromLocalStorage && setClickedButton(clickedButtonFromLocalStorage);
    testAnswersFromLocalStorage && setTestAnswers(testAnswersFromLocalStorage);
  };

  const updateSessionId = useCallback(
    async (newSessionId: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'sessionId',
        newVariable: newSessionId,
        oldVariable: sessionId,
        setter: setSessionId,
        getter: () => sessionId,
      });
    },
    [sessionId]
  );

  const updatePaymentSystem = useCallback(
    async (newPaymentSystem: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'paymentSystem',
        newVariable: newPaymentSystem,
        oldVariable: paymentSystem,
        setter: setPaymentSystem,
        getter: () => paymentSystem,
      });
    },
    [paymentSystem]
  );

  const updateLocale = useCallback(
    async (newLocale: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'locale',
        newVariable: newLocale,
        oldVariable: locale,
        setter: setLocale,
        getter: () => locale,
      });
    },
    [locale]
  );

  const updateQuestionId = useCallback(
    async (newQuestionId: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'questionId',
        newVariable: newQuestionId,
        oldVariable: questionId,
        setter: setQuestionId,
        getter: () => questionId,
      });
    },
    [questionId]
  );

  const updateAnswer = useCallback(
    async ({ questionId, answerId }: Answer) => {
      const otherAnswers = answers.filter(item => item.questionId !== questionId);
      const newAnswers = [...otherAnswers, { questionId, answerId }];
      update({
        contextPrefix: 'se',
        variableName: 'answers',
        newVariable: newAnswers,
        oldVariable: answers,
        setter: setAnswers,
        getter: () => answers,
      });
    },
    [answers]
  );

  const updateAllAnswers = useCallback(
    async (newAnswers: Answer[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'answers',
        newVariable: newAnswers,
        oldVariable: answers,
        setter: setAnswers,
        getter: () => answers,
      });
    },
    [answers]
  );

  const updateFormData = useCallback(async (formData: FormData) => {
    update({
      contextPrefix: 'se',
      variableName: 'formData',
      newVariable: formData,
      oldVariable: formData,
      setter: setFormData,
      getter: () => formData,
    });
  }, []);

  const updateCheckoutId = useCallback(
    async (newCheckoutId: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'checkoutId',
        newVariable: newCheckoutId,
        oldVariable: checkoutId,
        setter: setCheckoutId,
        getter: () => checkoutId,
      });
    },
    [checkoutId]
  );

  const updatePaymentStatus = useCallback(
    async (newPaymentStatus: string) => {
      update({
        contextPrefix: 'se',
        variableName: 'paymentStatus',
        newVariable: newPaymentStatus,
        oldVariable: paymentStatus,
        setter: setPaymentStatus,
        getter: () => paymentStatus,
      });
    },
    [paymentStatus]
  );

  const updateStage = useCallback(
    async (newStage: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'stage',
        newVariable: newStage,
        oldVariable: stage,
        setter: setStage,
        getter: () => stage,
      });
    },
    [stage]
  );

  const updateClickedButton = useCallback(
    async (newClickedButton: string | null) => {
      update({
        contextPrefix: 'se',
        variableName: 'clickedButton',
        newVariable: newClickedButton,
        oldVariable: clickedButton,
        setter: setClickedButton,
        getter: () => clickedButton,
      });
    },
    [clickedButton]
  );

  const updateTestAnswers = useCallback(
    async (newTestAnswers: string[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'testAnswers',
        newVariable: newTestAnswers,
        oldVariable: testAnswers,
        setter: setTestAnswers,
        getter: () => testAnswers,
      });
    },
    [testAnswers]
  );

  const resetTestState = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'stage',
      newVariable: 1,
      oldVariable: stage,
      setter: setStage,
      getter: () => stage,
    });
    update({
      contextPrefix: 'se',
      variableName: 'clickedButton',
      newVariable: null,
      oldVariable: clickedButton,
      setter: setClickedButton,
      getter: () => clickedButton,
    });
    update({
      contextPrefix: 'se',
      variableName: 'testAnswers',
      newVariable: [],
      oldVariable: testAnswers,
      setter: setTestAnswers,
      getter: () => testAnswers,
    });
  }, [stage, clickedButton, testAnswers]);

  // Add new methods for emotional intelligence test
  const updateEmotionalStage = useCallback(
    async (newStage: number) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalStage',
        newVariable: newStage,
        oldVariable: emotionalStage,
        setter: setEmotionalStage,
        getter: () => emotionalStage,
      });
    },
    [emotionalStage]
  );

  const updateEmotionalClickedButton = useCallback(
    async (newClickedButton: string | null) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalClickedButton',
        newVariable: newClickedButton,
        oldVariable: emotionalClickedButton,
        setter: setEmotionalClickedButton,
        getter: () => emotionalClickedButton,
      });
    },
    [emotionalClickedButton]
  );

  const updateEmotionalTestAnswers = useCallback(
    async (newTestAnswers: number[]) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalTestAnswers',
        newVariable: newTestAnswers,
        oldVariable: emotionalTestAnswers,
        setter: setEmotionalTestAnswers,
        getter: () => emotionalTestAnswers,
      });
    },
    [emotionalTestAnswers]
  );

  const resetEmotionalTestState = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'emotionalStage',
      newVariable: 1,
      oldVariable: emotionalStage,
      setter: setEmotionalStage,
      getter: () => emotionalStage,
    });
    update({
      contextPrefix: 'se',
      variableName: 'emotionalClickedButton',
      newVariable: null,
      oldVariable: emotionalClickedButton,
      setter: setEmotionalClickedButton,
      getter: () => emotionalClickedButton,
    });
    update({
      contextPrefix: 'se',
      variableName: 'emotionalTestAnswers',
      newVariable: [],
      oldVariable: emotionalTestAnswers,
      setter: setEmotionalTestAnswers,
      getter: () => emotionalTestAnswers,
    });
  }, [emotionalStage, emotionalClickedButton, emotionalTestAnswers]);

  const fetchAllSessionData = () => ({
    sessionId,
    locale,
    questionId,
    answers,
    formData,
    checkoutId,
    paymentStatus,
    stage,
    clickedButton,
    testAnswers,
    emotionalStage,
    emotionalClickedButton,
    emotionalTestAnswers,
  });

  const updateAllSessionData = async (data: SessionData) => {
    const {
      sessionId,
      locale,
      questionId,
      answers,
      formData,
      checkoutId,
      paymentStatus,
      stage,
      clickedButton,
      testAnswers,
      emotionalStage,
      emotionalClickedButton,
      emotionalTestAnswers,
    } = data;

    updateSessionId(sessionId);
    updateLocale(locale);
    updateQuestionId(questionId);
    updateAllAnswers(answers);
    updateFormData(formData);
    updateCheckoutId(checkoutId);
    updatePaymentStatus(paymentStatus);
    updateStage(stage || 1);
    updateClickedButton(clickedButton || null);
    updateTestAnswers(testAnswers || []);
    // Update emotional intelligence test fields
    updateEmotionalStage(emotionalStage || 1);
    updateEmotionalClickedButton(emotionalClickedButton || null);
    updateEmotionalTestAnswers(emotionalTestAnswers || []);
  };

  const resetAnswers = useCallback(async () => {
    update({
      contextPrefix: 'se',
      variableName: 'answers',
      newVariable: [],
      oldVariable: answers,
      setter: setAnswers,
      getter: () => answers,
    });
  }, [answers]);

  const updateResults = useCallback(
    async (newResults: any) => {
      update({
        contextPrefix: 'se',
        variableName: 'results',
        newVariable: newResults,
        oldVariable: results,
        setter: setResults,
        getter: () => results,
      });
    },
    [results]
  );

  const updateStripeInvoices = useCallback(
    async (newResults: { data: any[] }) => {
      update({
        contextPrefix: 'se',
        variableName: 'stripeInvoices',
        newVariable: newResults,
        oldVariable: stripeInvoices,
        setter: setStripeInvoices,
        getter: () => stripeInvoices,
      });
    },
    [stripeInvoices]
  );

  const updateSolidgateInvoices = useCallback(
    async (newResults: { data: any[] }) => {
      update({
        contextPrefix: 'se',
        variableName: 'solidgateInvoices',
        newVariable: newResults,
        oldVariable: solidgateInvoices,
        setter: setSolidgateInvoices,
        getter: () => solidgateInvoices,
      });
    },
    [solidgateInvoices]
  );

  const updateEmotionalScores = useCallback(
    async (newEmotionalScores: any) => {
      update({
        contextPrefix: 'se',
        variableName: 'emotionalScores',
        newVariable: newEmotionalScores,
        oldVariable: emotionalScores,
        setter: setEmotionalScores,
        getter: () => emotionalScores,
      });
    },
    [emotionalScores]
  );

  const updateMerchantData = useCallback(
    async (newMerchantData: InitConfig['merchantData']) => {
      update({
        contextPrefix: 'se',
        variableName: 'merchantData',
        newVariable: newMerchantData,
        oldVariable: merchantData,
        setter: setMerchantData,
        getter: () => merchantData,
      });
    },
    [merchantData]
  );

  return (
    <SessionContext.Provider
      value={{
        sessionId,
        merchantData,
        locale,
        questionId,
        answers,
        paymentSystem,
        getIsTrial,
        formData,
        checkoutId,
        paymentStatus,
        prices,
        siteConfig,
        results,
        emotionalScores,
        stage,
        clickedButton,
        testAnswers,
        stripeInvoices,
        solidgateInvoices,
        emotionalStage,
        emotionalClickedButton,
        emotionalTestAnswers,
        updateSessionId,
        updateLocale,
        updateQuestionId,
        updateAnswer,
        updatePaymentSystem,
        updateAllAnswers,
        updateFormData,
        updateCheckoutId,
        updatePaymentStatus,
        fetchAllSessionData,
        updateAllSessionData,
        resetAnswers,
        updateResults,
        updateEmotionalScores,
        updateMerchantData,
        updateStage,
        updateClickedButton,
        updateStripeInvoices,
        updateSolidgateInvoices,
        updateTestAnswers,
        resetTestState,
        updateEmotionalStage,
        updateEmotionalClickedButton,
        updateEmotionalTestAnswers,
        resetEmotionalTestState,
      }}>
      {children}
    </SessionContext.Provider>
  );
};

export default SessionContext;
