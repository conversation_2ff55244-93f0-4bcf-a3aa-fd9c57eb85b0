/**
 * Utility functions for menu item filtering and visibility logic
 */

export interface MenuItem {
  id: string;
  memberOnly?: boolean;
  hideIfLoggedIn?: boolean;
}

/**
 * Determines if a menu item should be visible based on current context
 * @param item - The menu item to check
 * @param activePath - Current active path/route
 * @param user - Current user object (null/undefined if not logged in)
 * @returns boolean indicating if the item should be shown
 */
export const shouldShowMenuItem = (
  item: MenuItem,
  activePath: string,
  user: any
): boolean => {
  // Hide pricing on results page
  if (activePath === '/results' && item.id === 'pricing') {
    return false;
  }

  // If user is not logged in, hide member-only items
  if (!user && item.memberOnly) {
    return false;
  }

  // If user is logged in, hide items that should be hidden when logged in
  if (user && item.hideIfLoggedIn) {
    return false;
  }

  return true;
};

/**
 * Alternative implementation using a more functional approach
 */
export const createMenuFilter = (activePath: string, user: any) => {
  return (item: MenuItem): boolean => {
    return shouldShowMenuItem(item, activePath, user);
  };
};

/**
 * Filters an array of menu items based on visibility rules
 * @param items - Array of menu items to filter
 * @param activePath - Current active path/route
 * @param user - Current user object
 * @returns Filtered array of visible menu items
 */
export const filterVisibleMenuItems = <T extends MenuItem>(
  items: T[],
  activePath: string,
  user: any
): T[] => {
  return items.filter(item => shouldShowMenuItem(item, activePath, user));
};

/**
 * Determines which Link component to use based on menu item properties
 * @param itemId - The menu item ID
 * @param itemPath - The menu item path (optional, for future URL-based logic)
 * @returns 'regular' for standard Next.js Link, 'i18n' for internationalized Link
 */
export const getLinkType = (itemId: string, itemPath?: string): 'regular' | 'i18n' => {
  // Use regular Link for insights to bypass internationalization
  if (itemId === 'insights') {
    return 'regular';
  }

  // Use regular Link for external URLs
  if (itemPath?.startsWith('http')) {
    return 'regular';
  }

  // Default to internationalized Link
  return 'i18n';
};

/**
 * Gets the display text for a menu item, with translation fallback
 * @param itemId - The menu item ID
 * @param originalTitle - The original title as fallback
 * @param translationKeys - Object mapping item IDs to translation keys
 * @param translateFn - Translation function
 * @returns Translated or fallback text
 */
export const getMenuItemText = (
  itemId: string,
  originalTitle: string,
  translationKeys: Record<string, string>,
  translateFn: (key: string) => string
): string => {
  const translationKey = translationKeys[itemId];
  return translationKey ? translateFn(translationKey) : originalTitle;
};
