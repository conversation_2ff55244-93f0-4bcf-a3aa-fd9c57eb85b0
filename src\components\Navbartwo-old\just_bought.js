import countryData from '../../../public/json/countries.json';
import data from '../../../public/json/names.json';

// Function to get neighboring countries by User country
const getNeighboringCountries = userCountry => {
  const result = countryData.find(country => country['User country'] === userCountry);

  if (result) {
    return [result['country 1'], result['country 2'], result['country 3'], result['country 4']].filter(Boolean); // Filter out any empty strings
  } else {
    return `No data found for ${userCountry}`;
  }
};

// Function to get all persons by country
const getByCountry = countryName => {
  return data
    .filter(person => person.Country === countryName) // Filter by country name
    .map(person => ({
      'First name': person['First name'],
      'Last name': person['Last name'],
      'IQ score': person['IQ score'],
    })); // Return specific fields
};

const shuffleArray = array => array.sort(() => Math.random() - 0.5);

// Main function to mix people based on country percentages and add "Country" field
const mixPeople = (countries, totalPeople = 100) => {
  // Step 1: Define base percentages
  let basePercentages = [0.55, 0.15, 0.15, 0.15];

  // Step 2: Adjust percentages based on available countries
  const validCountries = countries.filter(country => getByCountry(country).length > 0);
  const adjustedPercentages = basePercentages.slice(0, validCountries.length);
  const sumAdjustedPercentages = adjustedPercentages.reduce((sum, val) => sum + val, 0);
  const normalizedPercentages = adjustedPercentages.map(p => p / sumAdjustedPercentages);

  // Step 3: Calculate number of people to select from each country
  const peoplePerCountry = normalizedPercentages.map(percent => Math.floor(totalPeople * percent));

  // Step 4: Fetch and randomly select people from each country, adding "Country" field
  const selectedPeople = validCountries.flatMap((country, index) => {
    const people = shuffleArray(getByCountry(country)); // Shuffle to randomize
    return people.slice(0, peoplePerCountry[index]).map(person => ({
      ...person, // Spread the original person object
      Country: country, // Add the new "Country" field
    }));
  });

  // Step 5: Shuffle the final array of selected people
  return shuffleArray(selectedPeople);
};

export default function JustBoughtFunction(country) {
  var neightboringCountries = getNeighboringCountries(country);

  var result = mixPeople(neightboringCountries, 20);

  return result;
}
