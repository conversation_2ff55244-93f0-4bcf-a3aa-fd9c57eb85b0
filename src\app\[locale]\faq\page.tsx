'use client';

import '@/sass/faq.scss';
import { useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import FaqAccordion from '@/components/Accordions/FaqAccordion';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { PostHogEventEnum } from '@/store/types';

const Faq = () => {
  const t = useTranslations('faq');
  const { captureEvent } = usePostHogAnalytics();
  
  const data = useMemo(() => [
    {
      title: t('question1_title'),
      text: (
        <p>
          {t('question1_text')}
        </p>
      ),
    },
    {
      title: t('question2_title'),
      text: (
        <p>
          <a href={t('question2_link')} className="underline text-[#FF932F]">
            {t('question2_text')}
          </a>
        </p>
      ),
    },
    {
      title: t('question3_title'),
      text: (
        <p>
          {t('question3_text')}
        </p>
      ),
    },
    {
      title: t('question4_title'),
      text: (
        <p>
          {t('question4_text')}
        </p>
      ),
    },
    {
      title: t('question5_title'),
      text: (
        <p>
          {t('question5_text').split('\n\n').map((paragraph, index) => (
            <span key={index}>
              {paragraph}
              {index < t('question5_text').split('\n\n').length - 1 && (
                <>
                  <br />
                  <br />
                </>
              )}
            </span>
          ))}
        </p>
      ),
    },
    {
      title: t('question6_title'),
      text: (
        <p>
          {t('question6_text').split('\n\n').map((paragraph, index) => (
            <span key={index}>
              {paragraph}
              {index < t('question6_text').split('\n\n').length - 1 && (
                <>
                  <br />
                  <br />
                </>
              )}
            </span>
          ))}
        </p>
      ),
    },
    {
      title: t('question7_title'),
      text: (
        <p>
          {t('question7_text').split('\n\n').map((paragraph, index) => (
            <span key={index}>
              {paragraph}
              {index < t('question7_text').split('\n\n').length - 1 && (
                <>
                  <br />
                  <br />
                </>
              )}
            </span>
          ))}
        </p>
      ),
    },
  ], [t]);

  useEffect(() => {
    captureEvent(PostHogEventEnum.GO_TO_FAQ_PAGE, {});
    // eslint-disable-next-line
  }, []);

  return (
    <section className={`flex flex-wrap justify-between max-w-[1440px] m-auto`}>
      <div className="flex flex-col">
        <div style={{ maxWidth: 540 }}>
          <h1 className="mb-10" style={{ maxWidth: 353 }}>
            {t('title')}
          </h1>
        </div>
        <div className="" style={{ maxWidth: 736 }}>
          {data.map((item, index) => (
            <div key={index}>
              <FaqAccordion {...{ item, index }} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Faq;
