import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import useGetMerchantData from '@/hooks/useGetMerchantData';
import '@/sass/form.scss';
import '@/sass/spinner.scss';
import SessionContext from '@/store/SessionContext';
import { PostHogEventEnum } from '@/store/types';
import UiContext from '@/store/UiContext';
import { functions } from '@/utils/firebase';
import Payment, { ClientSdkInstance } from '@solidgate/react-sdk';
import { httpsCallable } from 'firebase/functions';
import { useRouter } from '@/lib/i18n/navigation';
import { useContext, useRef, useState } from 'react';

const formParams = {
  allowSubmit: false,
};
const trackingPostback = httpsCallable(functions, 'trackingPostback');

export default function CheckoutFormSolidgate() {
  const { captureEvent } = usePostHogAnalytics();
  const [message, setMessage] = useState<string | null>(null);
  const [form, setForm] = useState<ClientSdkInstance | null>(null);
  const { prices, getIsTrial, merchantData, updateMerchantData } = useContext(SessionContext);
  const { getMerchantData } = useGetMerchantData();
  const { time } = useContext(UiContext);
  const router = useRouter();
  const isTrial = getIsTrial();
  const compliantVersion = time < 330;
  const appleContainerRef = useRef(null);
  let clickId: string | null = typeof window != 'undefined' ? window.clickflare?.data?.event_tokens?.click_id : null;
  let landingUrl = typeof localStorage != 'undefined' ? localStorage?.getItem('landingUrl') : null;
  if (!clickId && landingUrl && landingUrl != 'null') {
    try {
      const parsedLandingUrl = new URL(landingUrl);
      if (!clickId) clickId = parsedLandingUrl.searchParams.get('click_id');
    } catch (e) {
      console.error('Error parsing landingUrl:', e);
    }
  }

  const handleOnReadyPaymentInstance = (form: ClientSdkInstance) => {
    setForm(form);
  };

  const onSuccess = () => {
    captureEvent(PostHogEventEnum.PAYMENT_SUCCESS, { paymentSystem: 'solidgate', ...merchantData });
    router.push('/results?init=1');
  };

  const onFail = () => {
    setMessage('Something went wrong.');
    document.getElementById('paymentDetails')!.scrollIntoView();
  };

  const onSubmit = () => {
    captureEvent(PostHogEventEnum.CHECKOUT_CONFIRMED, { paymentSystem: 'solidgate' });
    form?.submit();
  };

  return (
    merchantData && (
      <div id="paymentDetails" className="flex justify-center">
        <div className="flex flex-col grow">
          <div className="mx-5">
            {message && <div className="mb-5 mt-2 text-red-500 font-bold text-lg">{message}</div>}
            {message && (
              <button
                onClick={async () => {
                  const fetchedMerchantData = await getMerchantData({ clickId, time, isTrial });
                  updateMerchantData(fetchedMerchantData);
                  setMessage(null);
                }}
                className="flex button danger text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px]">
                <span>Retry payment</span>
              </button>
            )}
            <div className="flex justify-between bg-grey-bg p-5 mx-4 md:mx-0 mb-4">
              <h5 className="items-center">Total</h5>
              {getIsTrial() ? <h5>{prices.oneTime.formatted}</h5> : <h5>{prices.subscription.formatted}</h5>}
            </div>
            {compliantVersion && getIsTrial() && (
              <p className="text-sm mb-6">
                Price of {prices.oneTime.formatted}
                {prices.vatIncluded ? ' (incl. VAT)' : ''} for a 2-day trial. At the end of the trial period and without
                cancellation from your side, our offer will be automatically renewed as a non-binding subscription, at
                the price of {prices.subscription.formatted} per month
                {prices.vatIncluded ? ' (incl. VAT)' : ''}.
              </p>
            )}
            <div ref={appleContainerRef}></div>
            <Payment
              merchantData={merchantData}
              onReadyPaymentInstance={handleOnReadyPaymentInstance}
              applePayContainerRef={appleContainerRef}
              onSuccess={onSuccess}
              onFail={onFail}
              formParams={formParams}
              width="100%"
            />

            <button
              id="submit"
              onClick={onSubmit}
              className="flex w-full button primary text-base md:text-xl font-semibold justify-center mt-3 rounded-[10px] leading-">
              <span id="button-text">
                {compliantVersion && getIsTrial() ? 'Start trial and Get IQ results' : 'Get IQ Results'}
              </span>
            </button>
          </div>
        </div>
      </div>
    )
  );
}
