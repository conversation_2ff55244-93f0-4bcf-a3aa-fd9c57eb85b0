"use client";
import Image from 'next/image';
import React, { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

const Logo: React.FC = () => {
  const { siteConfig } = useContext(SessionContext);
  return (
    <Image
      src={siteConfig.logo.path} // Path relative to 'public'
      alt="Logo"
      width={siteConfig.logo.width}
      height={siteConfig.logo.height}
      className="max-w-[215px] md:max-w-[300px] z-20"
    />
  );
};

export default Logo;
