import { httpsCallable } from 'firebase/functions';
import { headers } from 'next/headers';
import { functions } from '@/utils/firebase';

export type Prices = {
    country: string;
    currency: string;
    symbol: string;
    subscription: { amount: number; formatted: string; };
    oneTime: { amount: number; formatted: string };
    vatIncluded: boolean;
}

export const priceList: { [country: string]: Prices } = {
    'us': { country: 'us', currency: 'usd', symbol: '$', subscription: { amount: 39.90, formatted: '$39.90' }, oneTime: { amount: 0.99, formatted: '$0.99' }, vatIncluded: false },
    'ca': { country: 'ca', currency: 'usd', symbol: '$', subscription: { amount: 39.90, formatted: '$39.90' }, oneTime: { amount: 0.99, formatted: '$0.99' }, vatIncluded: false },
    'gb': { country: 'gb', currency: 'gbp', symbol: '£', subscription: { amount: 35.90, formatted: '£35.90' }, oneTime: { amount: 0.79, formatted: '£0.79' }, vatIncluded: true },
    'br': { country: 'br', currency: 'brl', symbol: 'R$', subscription: { amount: 199, formatted: 'R$199' }, oneTime: { amount: 3.99, formatted: 'R$3.99' }, vatIncluded: false },
    'eu': { country: 'eu', currency: 'eur', symbol: '€', subscription: { amount: 44.90, formatted: '€44.90' }, oneTime: { amount: 0.7, formatted: '€0.70' }, vatIncluded: true },
}

export const getPrices = async () => {
  const country = headers().get('x-vercel-ip-country')?.toLowerCase();
  const prices = country && priceList[country] ? priceList[country] : priceList['eu'];

  // const ip = headers().get('x-ip');
  // if (ip && prices.country === 'eu') {
  //   const calculateTax = httpsCallable(functions, 'calculateTax');
  //   prices.vatIncluded = true;
  //   const tax: any = await calculateTax({ country, ip }).then(x => x.data).catch(err => null);
  //   prices.subscription.amount = tax?.amount_total ? tax?.amount_total / 100 : prices.subscription.amount;
  //   prices.subscription.formatted = new Intl.NumberFormat('en-US', { style: 'currency', currency: prices.currency }).format(prices.subscription.amount);
  // }

  return prices;
}