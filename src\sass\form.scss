/*form styles*/
#msform {
  width: 390px;
  margin: 40px auto;
  text-align: center;
}
/*inputs*/
#msform input:not([type='checkbox']),
#msform textarea {
  padding: 14px 0 14px 14px;
  background: #fff;
  border: 1px solid rgba(193, 207, 233, 0.45);
  border-radius: 8px;
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 400;
  color: #191919;
  height: 48px;
  outline: none;
}

#msform input:disabled {
  padding: 14px 0 14px 14px;
  background: #dbdbdb;
  border: 1px solid rgba(193, 207, 233, 0.45);
  border-radius: 8px;
  margin-bottom: 10px;
  width: 100%;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 400;
  color: #191919;
  height: 48px;
  outline: none;
}

#msform input:focus,
#msform input:active,
#msform textarea:focus,
#msform textarea:active {
  border: 1px solid theme('colors.primary') !important;
}

#msform input[type='number'] {
  height: 48px;
  position: relative;
  font-size: 16px;
  text-align: left;
  /*background-image: url('data:image/svg+xml;utf8,%3Csvg%20version%3D%221.1%22%20viewBox%3D%220%200%2050%2067%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill%3D%22none%22%20stroke-width%3D%222%22%3E%3Cline%20x1%3D%221%22%20x2%3D%2250%22%20y1%3D%2233.5%22%20y2%3D%2233.5%22%20stroke%3D%22%23D8D8D8%22%2F%3E%3Cpolyline%20transform%3D%22translate(25%2020)%20rotate(45)%20translate(-25%20-20)%22%20points%3D%2219%2026%2019%2014%2032%2014%22%20stroke%3D%22%23000%22%2F%3E%3Cpolyline%20transform%3D%22translate(25%2045)%20rotate(225)%20translate(-25%20-45)%22%20points%3D%2219%2052%2019%2039%2032%2039%22%20stroke%3D%22%23000%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E');*/
  /*background-image: url('../../public/form/arrows.svg');*/
  background-position: 100% 100%;
  /*background-size: contain;*/
  background-repeat: no-repeat;
  caret-color: transparent;
}

#msform input[type='number']::-webkit-inner-spin-button {
  /*-webkit-appearance: none !important;*/
  opacity: 0 !important;
  background: transparent !important;
  border-width: 0px;
  padding: 8px;
  height: 48px;
  width: 50px;
  cursor: pointer;
  font-size: 46px;
  z-index: 999;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

@-moz-document url-prefix() {
  .arrows {
    display: none;
  }
}

.arrows > svg > path:hover {
  fill: theme('colors.primary');
}

/*buttons*/
.action-button {
  padding: 10px;
  margin-top: 24px;
  background: theme('colors.primary');
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  border: 1px solid theme('colors.primary');
  border-radius: 10px;
  cursor: pointer;
  text-decoration: none;
  line-height: 30px;
  letter-spacing: -0.2px;
  width: 100%;
}

.action-button:disabled,
button[disabled] {
  // background: #f6f9ff;
  // color: #8893ac;
  border: 1px solid #f6f9ff;
}

@media only screen and (max-width: 500px) {
  #msform {
    max-width: 90vw;
  }
  #msform input {
    min-width: 0;
  }
}

@media only screen and (max-height: 700px) {
  #msform {
    margin-top: 0;
    margin-bottom: 40px;
  }
}

/* Checkbox */
/* The container */
.checkbox-container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 20px;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border-radius: 4px !important;
}

/* On mouse-over, add a grey background color */
.checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.checkbox-container input:checked ~ .checkmark {
  background-color: theme('colors.primary');
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.checkbox-container .checkmark:after {
  left: 8px;
  top: 4px;
  width: 8px;
  height: 14px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
