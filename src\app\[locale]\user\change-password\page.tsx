'use client';

import { useContext, useState } from 'react';
import { useTranslations } from 'next-intl';
import { updatePassword } from 'firebase/auth';
import type { User } from 'firebase/auth';
import { auth } from '@/utils/firebase';
import { Loader2 } from 'lucide-react';
import { UserContext } from '@/store/UserContext';
import requireAuth from '@/components/requireAuth';

const ChangePassword = () => {
  const t = useTranslations('members.change_password');
  const [password1, setPassword1] = useState('');
  const [password2, setPassword2] = useState('');
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState(false);
  const { logout } = useContext(UserContext);


  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'password1') setPassword1(value);
    if (name === 'password2') setPassword2(value);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLButtonElement>) => {
    setErrorMessage('');
    setSuccess(false);
    e.preventDefault();

    setLoading(true);
    try {
      if (password1.length < 6) {
        setError(true);
        setErrorMessage(t('messages.password_too_short'));
      } else if (password1 !== password2) {
        setError(true);
        setErrorMessage(t('messages.passwords_dont_match'));
      } else {
        await updatePassword(auth.currentUser as User, password1);
        setSuccess(true);
        setPassword1('');
        setPassword2('');
      }
    } catch (e: any) {
      console.log(e)
      alert(t('messages.change_error'));
      logout();
    } finally {
      setLoading(false);
    }
  };

  return (
    //@ts-ignore
    <form id='msform' onSubmit={(e) => e.preventDefault()} noValidate>
      <h1
        className='fs-title mb-5'
        style={{
          fontSize: 32,
          lineHeight: '112.5%',
        }}
      >
        {t('title')}
      </h1>
      <fieldset>
        {error && <div className='mb-5'>{errorMessage}</div>}
        {success && <div className='mb-5 font-bold text-primary'>{t('messages.change_success')}</div>}
        <input type='password' name='password1' placeholder={t('new_password_placeholder')} onChange={handleChange} value={password1} required />
        <input
          type='password'
          name='password2'
          value={password2}
          placeholder={t('confirm_password_placeholder')}
          onChange={handleChange}
          required
        />
        <button onClick={handleSubmit} className='action-button flex justify-center' style={{ marginTop: 4 }}>
          {loading ? <Loader2 className='h-4 w-4 animate-spin m-[5px] mr-2' /> : ''}
          {t('btn_submit')}
        </button>
      </fieldset>
    </form>
  );
};

export default requireAuth(ChangePassword, []);
