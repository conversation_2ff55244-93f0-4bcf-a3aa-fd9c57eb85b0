'use client'; // Enable client-side interactivity
import React from 'react';
import { useRouter } from '@/lib/i18n/navigation'; // Import the router hook

const LoginButton: React.FC = () => {
  const router = useRouter(); // Initialize the router
  const onLoginClickHandler = () => {
    router.push('/login'); // Navigate to the /login route
  };
  return (
    <button
      className="bg-white rounded-[10px] w-[87px] h-[38px] md:w-[240px] md:h-[62px] text-[#8C36D0] font-ppmori font-semibold text-[16px] md:text-[20px] leading-[24px] shadow-[0px_2px_14px_rgba(65,0,81,0.1)] md:shadow-none tracking-[-0.03em] z-20"
      onClick={onLoginClickHandler}>
      Login
    </button>
  );
};

export default LoginButton;
