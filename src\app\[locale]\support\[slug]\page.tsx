'use client';

import { useRouter } from '@/lib/i18n/navigation';
import { useParams } from 'next/navigation';

type Question = {
  question: string;
  answer: JSX.Element;
};

const faqData: { [key: string]: Question } = {
  'automatic-renewals': {
    question: 'Are there automatic renewals on my subscription?',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Yes. When you take the test, a small fee grants you a 2-day trial with full access to our platform and you
          will receive a welcome email with our terms & conditions upon sign-up.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          After the trial ends, your subscription will automatically renew unless you cancel before the trial period
          expires. You can manage or cancel your subscription anytime through your account settings.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          For full details, please refer to section 3, Pricing and Payment terms, on our{' '}
          <a href="https://blog.iqinternational.org/terms-and-conditions/" className="underline text-[#FF932F]">
            Terms and Conditions
          </a>{' '}
          and make sure you understand the renewal policy before subscribing.
        </p>
      </>
    ),
  },
  'update-email': {
    question: 'I need to update the email linked to my subscription',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          To update your email address subscription, feel free to use our IQ Assistant and contact us to escalate this
          matter to one of our agents.
        </p>
      </>
    ),
  },
  'cancel-subscription': {
    question: 'How can I cancel my subscription?',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          To cancel your subscription, feel free to use our <span className="text-[#FF932F]">IQ Support Chatbot</span>
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Or you can follow this step-by-step guide to deactivate your subscription:
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          To cancel your subscription, navigate to &apos;My Subscription&apos; within your account settings, then select
          the &apos;Click here to cancel your subscription&apos; link.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          When you cancel a subscription, you will retain access for the remainder of the period you have already paid
          for.
        </p>
      </>
    ),
  },
  'lose-access-to-results': {
    question: 'Will I lose access to my results if I cancel my subscription?',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          No, your test results will remain available even after your subscription is cancelled. You can continue to
          access your previous scores and insights at any time.
        </p>

        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>Our Video Academy with lessons to boost critical thinking</li>
          <li>Additional IQ training exercises to enhance your cognitive skills</li>
          <li>Other tests designed to provide insights into your personality</li>
        </ul>

        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          You will also no longer be able to retake the IQ Test unless your subscription is reactivated.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          To regain access to these features, you can re-subscribe at any time through your account settings, we’d be
          glad to have you back whenever you&apos;re ready.
        </p>
      </>
    ),
  },
  'subscription-charge': {
    question: 'Why am I seeing a subscription charge?',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          At <span className="text-[#FF932F]">IQ International</span>, we’re committed to being clear and honest with
          our users. We understand that subscription services can sometimes be confusing, so we aim to make everything
          as simple and transparent as possible.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          When you created your account, you agreed to our{' '}
          <a href="https://blog.iqinternational.org/terms-and-conditions/" className="underline text-[#FF932F]">
            Terms & Conditions.
          </a>{' '}
          As part of that process, we clearly showed you the details of your plan—including a 2-day trial for $0.99,
          followed by your selected subscription rate. You were required to confirm and accept these terms before
          continuing.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          According to <span className="text-[#FF932F]">Section 2.1</span>, your subscription begins with a{' '}
          <span className="text-[#FF932F]">2-day trial period</span> for a one-time fee of{' '}
          <span className="text-[#FF932F]">$0.99</span>. If you don’t cancel within the trial period, your selected
          subscription plan will automatically begin, and the regular rate will be charged starting on Day 3.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          We also sent a welcome email at the time of sign-up, which included:
        </p>
        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>A summary of your subscription</li>
          <li>Trial duration and pricing details</li>
          <li>The scheduled billing start date</li>
          <li>Easy-to-follow instructions for cancellation</li>
        </ul>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          As described in <span className="text-[#FF932F]">Sections 3.1 and 3.2</span>:
        </p>
        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>
            Your subscription <span className="text-[#FF932F]">will automatically renew</span> unless canceled before
            the renewal date
          </li>
          <li>To avoid being charged again, cancellation must be completed before the next billing cycle</li>
          <li>
            You can manage or cancel your subscription at any time through our{' '}
            <span className="text-[#FF932F]">self-service portal</span>, without needing to contact support
          </li>
        </ul>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Per <span className="text-[#FF932F]">Section 2.2</span>, you authorized recurring billing using the payment
          method you provided during registration. All transactions are processed through a{' '}
          <span className="text-[#FF932F]">PCI-DSS certified, bank-grade secure payment system</span> that uses{' '}
          <span className="text-[#FF932F]">advanced encryption and regulatory-level security standards</span> to keep
          your payment information safe.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Here’s a clear overview of how the billing works:
        </p>
        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>
            <span className="text-[#FF932F]">Days 1–2</span>: Trial period for $0.99
          </li>
          <li>
            <span className="text-[#FF932F]">Day 3 onward</span>: Your regular subscription price will be charged
            (unless you cancel before this date)
          </li>
        </ul>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          If anything is unclear or you need help with your account, our support team is here to assist you. We
          appreciate your trust in IQ International and are committed to making your experience secure, smooth, and
          hassle-free.
        </p>
      </>
    ),
  },
  'request-refund': {
    question: 'I want to request a refund',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Please note that eligibility for a refund is strictly determined by the conditions outlined in{' '}
          <span className="text-[#FF932F]">Section 6 (Refund Policy)</span> of our{' '}
          <a href="https://blog.iqinternational.org/terms-and-conditions/" className="underline text-[#FF932F]">
            Terms & Conditions
          </a>
          , which you agreed to at the time of sign-up.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          As outlined in <span className="text-[#FF932F]">Section 6.4.3</span>, if your refund request meets the
          criteria and is approved, it will be processed within <span className="text-[#FF932F]">3 business days</span>.
          Most refunds appear in your account within <span className="text-[#FF932F]">1–2 business days</span> after
          processing, though in some cases it may take up to <span className="text-[#FF932F]">5–10 business days</span>,
          depending on your bank’s processing time.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          According to <span className="text-[#FF932F]">Section 2.1</span>, your subscription begins with a{' '}
          <span className="text-[#FF932F]">2-day trial period</span> for a one-time fee of{' '}
          <span className="text-[#FF932F]">$0.99</span>. If you don’t cancel within the trial period, your selected
          subscription plan will automatically begin, and the regular rate will be charged starting on Day 3.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          For transactions involving <span className="text-[#FF932F]">currency conversion</span>:
        </p>
        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>Exchange rates are determined by your bank or card issuer</li>
          <li>
            Any conversion fees charged by your bank are <span className="text-[#FF932F]">non-refundable</span>
          </li>
          <li>You may see these as separate charges on your bank statement</li>
        </ul>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          As described in <span className="text-[#FF932F]">Sections 3.1 and 3.2</span>:
        </p>
        <ul className="list-disc pl-[25px] text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          <li>
            Your subscription <span className="text-[#FF932F]">will automatically renew</span> unless canceled before
            the renewal date
          </li>
          <li>To avoid being charged again, cancellation must be completed before the next billing cycle</li>
          <li>
            You can manage or cancel your subscription at any time through our{' '}
            <span className="text-[#FF932F]">self-service portal</span>, without needing to contact support
          </li>
        </ul>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Once the refund is processed on our end, we’re unable to track its progress within your bank’s system. If more
          than <span className="text-[#FF932F]">10 business days</span> have passed and you haven’t received the refund
          (which is rare), we recommend contacting your bank directly with your transaction details.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Please remember:{' '}
          <span className="text-[#FF932F]">refunds are only issued to the original payment method</span> used during
          sign-up. We cannot process refunds to any alternative account or payment method.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          If your card is <span className="text-[#FF932F]">expired or blocked</span>, the refund will still be sent to
          that card, and your bank will manage crediting the funds to your account.
        </p>
      </>
    ),
  },
  'expect-charge': {
    question: 'I did not expect this charge',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          IQ International is a subscription-based service. When you sign up to take the test, you are required to enter
          your email address, choose a payment method, and agree to a 2-day trial offer, after which a monthly
          subscription begins automatically unless cancelled beforehand.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          All subscription details — including pricing, billing cycle, and how to cancel — are clearly shown during
          checkout. We also send a welcome email upon registration with everything you need to manage your account and
          subscription settings.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          According to <span className="text-[#FF932F]">Section 2.1</span>, your subscription begins with a{' '}
          <span className="text-[#FF932F]">2-day trial period</span> for a one-time fee of{' '}
          <span className="text-[#FF932F]">$0.99</span>. If you don’t cancel within the trial period, your selected
          subscription plan will automatically begin, and the regular rate will be charged starting on Day 3.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          For more, see our{' '}
          <a href="https://blog.iqinternational.org/terms-and-conditions/" className="underline text-[#FF932F]">
            Terms and Conditions
          </a>
          .
        </p>
      </>
    ),
  },
  'contact-us': {
    question: 'Contact us',
    answer: (
      <>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Need help? We’re here for you, anytime.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          Our <span className="text-[#FF932F]">IQ Support Chatbot</span> is available 24/7 to help you with quick
          answers and guidance.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          For more detailed inquiries, our <span className="text-[#FF932F]">Customer Support Team</span> is also
          available around the clock. While we usually respond within a few hours, response times may be slightly longer
          during weekends and public holidays.
        </p>
        <p className="text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69]">
          We encourage you to explore our Support area, where you’ll find up-to-date answers to the most common
          questions.
        </p>
      </>
    ),
  },
};
export default function CustomerSupportDetail() {
  const router = useRouter();
  const { slug } = useParams<{ slug: string }>();

  const handleBackToList = () => {
    router.push('/support');
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  return (
    <div className="w-full max-w-[1120px] mx-auto flex flex-col md:mt-[30px]">
      <div className="truncate text-[15px] leading-[24px] tracking-[0] font-normal text-[#454F69] px-[33px] md:px-0 mt-[20px] md:mt-0">
        <span className="hover:underline cursor-pointer" onClick={handleBackToHome}>
          Home
        </span>{' '}
        {'>'}{' '}
        <span className="hover:underline cursor-pointer" onClick={handleBackToList}>
          Support
        </span>{' '}
        {'>'} <span className="text-[#FF932F]">{faqData[slug].question}</span>
      </div>
      <div className="w-full max-w-[1120px] mt-[15px] flex flex-col gap-[18px] px-[33px] md:px-0 mb-[48px] md:mb-0">
        <h2 className="text-[20px] font-semibold leading-[27px] tracking-[0] text-[#0D0D0E]">
          {faqData[slug].question}
        </h2>

        {faqData[slug].answer}
      </div>
    </div>
  );
}
