'use client';

import { useContext, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { ListStyleCheck } from '@/components/Icons/ListStyleCheck';
import SessionContext from '@/store/SessionContext';
import { Link } from '@/lib/i18n/navigation';

const Pricing = () => {
  const t = useTranslations('pricing');
  const { prices, getIsTrial } = useContext(SessionContext);

  const features = useMemo(() => [
    t('feature_certificate'),
    t('feature_report'),
    t('feature_training')
  ], [t]);

  return (
    <section className={`flex flex-wrap justify-between max-w-[1440px] m-auto`} style={{}}>
      <div className="flex flex-col">
        <h1 className="text-5xl md:text-6xl">{t('title')}</h1>
        <ul className="mt-10 gap-2 flex flex-col">
          {features.map((text, i) => (
            <li key={i} className="flex items-center">
              <ListStyleCheck className="w-[20px] mr-2" />
              <span>{text}</span>
            </li>
          ))}
        </ul>
        <div className="flex gap-5 md:flex-row flex-col flex-wrap mt-10">
          {getIsTrial() && (
            <div className="flex flex-col items-center gap-7 border border-primary pb-5 rounded-md w-[320px]">
              <span className="p-3 bg-primary text-white font-semibold rounded-sm w-full text-center">
                {t('trial_header')}
              </span>
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-2">
                  <span className="text-4xl font-bold">{prices.oneTime.formatted}</span>
                  <span>{t('trial_period')}</span>
                </div>
                {prices.vatIncluded && <span>{t('vat_included')}</span>}
              </div>
            </div>
          )}
          <div className="flex flex-col items-center gap-7 border border-primary pb-5 rounded-md w-[320px]">
            <span className="p-3 bg-primary text-white font-semibold rounded-sm w-full text-center">
              {t('subscription_header')} {getIsTrial() && t('subscription_after_trial')}
            </span>
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-2">
                <span className="text-4xl font-bold">{prices.subscription.formatted}</span>
                <span>{t('monthly_period')}</span>
              </div>
              {prices.vatIncluded && <span>{t('vat_included')}</span>}
            </div>
          </div>
        </div>
        <Link
          href={'/questions'}
          className="text-center hover:opacity-90 bg-primary text-2xl font-bold w-[320px] text-white p-3 rounded-md mt-10">
          {t('btn_start_iq_test')}
        </Link>
      </div>
    </section>
  );
};

export default Pricing;
