{"name": "iq-test-frontend-next-js-vercel", "version": "0.1.0", "private": true, "scripts": {"dev": "node generate-fonts.js && node generate-sitemap.js && next dev", "build": "node generate-fonts.js && node generate-sitemap.js && next build", "start": "next start", "coverage": "next test -- --coverage", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "@headlessui/react": "^2.1.8", "@heroicons/react": "^2.1.5", "@next/third-parties": "14.0.5-canary.38", "@sentry/nextjs": "7.105.0", "@solidgate/react-sdk": "^1.17.0", "@stripe/react-stripe-js": "^2.6.2", "@stripe/stripe-js": "^3.2.0", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.10", "chart.js": "^4.4.6", "cookies-next": "^4.1.1", "dayjs": "^1.11.12", "firebase": "^10.12.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "logrocket": "^8.1.0", "lscache": "^1.3.2", "lucide-react": "^0.365.0", "negotiator": "^0.6.3", "next": "^14.2.4", "next-axiom": "^1.1.1", "next-intl": "^4.1.0", "posthog-js": "^1.246.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-fast-marquee": "^1.6.5", "react-hotjar": "^6.3.1", "react-modal": "^3.16.1", "react-responsive-modal": "^6.4.2", "react-slick": "^0.30.2", "react-timer-hook": "^3.0.7", "recharts": "^2.15.0", "sharp": "^0.33.3", "slick-carousel": "^1.8.1", "stripe": "^16.2.0", "swiper": "^11.1.15", "uuid": "^9.0.1", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.0", "@types/lscache": "^1.3.4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/uuid": "^9.0.8", "autoprefixer": "^10.0.1", "cloc": "^2.0.0-cloc", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "sass": "^1.72.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}