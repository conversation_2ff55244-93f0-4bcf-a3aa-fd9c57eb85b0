import Image from 'next/image';

import UpsalePath from '@/components/UpsaleFlow/UpselPath';

export default function UpsaleOne({ handleNext }) {
  return (
    <div>
      <div className="container mx-auto">
        <div className="m-auto hidden md:flex rounded-2xl justify-center content-center md:w-[420px] bg-[#4BCA7D] my-10 mt-6 md:mt-6">
          <div className="p-5 flex-none w-16">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25.8478 12.9022C25.9757 13.0299 26.0771 13.1815 26.1463 13.3485C26.2155 13.5154 26.2511 13.6943 26.2511 13.875C26.2511 14.0557 26.2155 14.2346 26.1463 14.4015C26.0771 14.5685 25.9757 14.7201 25.8478 14.8478L16.2228 24.4728C16.0951 24.6007 15.9435 24.7021 15.7766 24.7713C15.6096 24.8405 15.4307 24.8761 15.25 24.8761C15.0693 24.8761 14.8904 24.8405 14.7235 24.7713C14.5565 24.7021 14.4049 24.6007 14.2772 24.4728L10.1522 20.3478C9.89419 20.0898 9.74924 19.7399 9.74924 19.375C9.74924 19.0101 9.89419 18.6602 10.1522 18.4022C10.4102 18.1442 10.7601 17.9992 11.125 17.9992C11.4899 17.9992 11.8398 18.1442 12.0978 18.4022L15.25 21.5561L23.9022 12.9022C24.0299 12.7743 24.1815 12.6729 24.3485 12.6037C24.5154 12.5345 24.6943 12.4989 24.875 12.4989C25.0557 12.4989 25.2346 12.5345 25.4016 12.6037C25.5685 12.6729 25.7201 12.7743 25.8478 12.9022ZM35.875 18C35.875 21.5353 34.8267 24.9913 32.8625 27.9308C30.8984 30.8703 28.1067 33.1614 24.8405 34.5143C21.5742 35.8673 17.9802 36.2212 14.5128 35.5315C11.0454 34.8418 7.86034 33.1394 5.36047 30.6395C2.86061 28.1397 1.15818 24.9547 0.468471 21.4872C-0.22124 18.0198 0.132745 14.4258 1.48566 11.1595C2.83858 7.8933 5.12966 5.10161 8.06919 3.13748C11.0087 1.17335 14.4647 0.125 18 0.125C22.7392 0.130005 27.2829 2.01487 30.634 5.36599C33.9851 8.71712 35.87 13.2608 35.875 18ZM33.125 18C33.125 15.0086 32.2379 12.0843 30.576 9.597C28.914 7.10971 26.5518 5.1711 23.7881 4.02632C21.0244 2.88155 17.9832 2.58202 15.0493 3.16562C12.1153 3.74922 9.42029 5.18974 7.30502 7.30501C5.18975 9.42028 3.74923 12.1153 3.16563 15.0493C2.58203 17.9832 2.88155 21.0244 4.02633 23.7881C5.17111 26.5518 7.10971 28.914 9.59701 30.576C12.0843 32.2379 15.0086 33.125 18 33.125C22.01 33.1204 25.8545 31.5255 28.69 28.69C31.5255 25.8545 33.1205 22.01 33.125 18Z"
                fill="white"
              />
            </svg>
          </div>
          <div className="text-white flex-1 p-4">
            <h4 className="text-white text-[18px]">Thank you!</h4>
            <p className="text-white text-[16px]">Your order was successful!</p>
          </div>
        </div>
        <UpsalePath section={1} />
        <div className="text-center text-[52px] text-[#191919] pt-[40px]">
          <h1 className="text-[28px] md:text-[30px] pt-4 pb-2 md:pb-4">Commit to a Smarter You!</h1>
          <p className="md:max-w-[600px] text-[15px] mx-auto hidden md:block">
            Kickstart your journey with one easy step today. Studies show that full commitment leads to better
            results—skip the trial and start sharpening your mind now!
          </p>
        </div>
        <div className="flex justify-between max-w-[840px] mx-auto mt-2 md:mt-8 mb-8 md:mb-16">
          <div className="w-[50%] md:w-[410px] p-2 pt-[25px] m-2 md:p-5 bg-white  rounded-xl shadow mt-16 md:mt-20">
            <h2 className="text-[20px] md:text-[36px] text-[#191919] tracking-[.01em] text-center md:text-left">
              $19.99 per week
            </h2>

            <div className="w-full h-[2px] bg-gray-100"></div>
            <h4 className="text-[13px] md:text-[18px] text-[#191919] py-3 text-center md:text-left">
              Billing period & price
            </h4>
            <div className="text-center md:text-left md:flex justify-between">
              <p className="text-[14px] md:text-[16px]">Billed every week</p>
              <p className="font-bold  tracking-[.03em]">$19.99</p>
            </div>
            <div className="text-center md:text-left md:flex justify-between py-3">
              <p className="text-[14px] md:text-[16px]">Billed in 4 weeks</p>
              <p className="font-bold  tracking-[.03em]">$79.96</p>
            </div>
            <button
              onClick={() => {
                handleNext();
              }}
              className="text-center hidden md:block w-full border font-bold py-3 text-[20px] rounded-xl">
              Continue with the Trial
            </button>
            <button
              onClick={() => {
                handleNext();
              }}
              className="text-center block md:hidden w-full border font-bold mt-0 py-3 text-[20px] rounded-xl">
              Start trial
            </button>
          </div>
          <div className="w-[50%] md:w-[410px] bg-primary bg-opacity-5 rounded-xl border-4 border border-primary">
            <div className="bg-primary py-4">
              <h1 className="text-center text-white text-[20px] md:text-[36px]">BEST VALUE 🎉</h1>
            </div>
            <div className="p-2 pt-3 m-2 md:m-0 md:pb-5">
              <h2 className="text-[20px] md:text-[36px] text-[#191919] pt-2 tracking-[.01em] text-center md:text-left">
                $1.78 / Day
              </h2>

              <div className="w-full h-[2px] bg-gray-100"></div>
              <h4 className="text-[13px] md:text-[18px] text-[#191919] py-3 text-center md:text-left">
                Billing period & price
              </h4>
              <div className="text-center md:text-left md:flex justify-between">
                <p className="text-[14px] md:text-[16px]">Every 4 weeks</p>
                <p className="font-bold  tracking-[.03em]">$49.99</p>
              </div>
              <div className="text-center md:text-left md:flex justify-between py-3">
                <p className="text-[14px] md:text-[16px]">Billed in 4 weeks</p>
                <p className="font-bold  tracking-[.03em]">$49.99</p>
              </div>
              <button
                onClick={() => {
                  handleNext();
                }}
                className="text-center hidden md:block w-full border font-bold py-3 text-[20px] bg-primary text-white rounded-xl">
                Skip the Trial & Get Access
              </button>
              <button
                onClick={() => {
                  handleNext();
                }}
                className="text-center block md:hidden w-full border font-bold py-3 text-[20px] bg-primary text-white rounded-xl">
                Skip trial
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
