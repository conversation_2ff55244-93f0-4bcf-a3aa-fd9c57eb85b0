const CookiePolicy = () => {
  return (
    <section className='cookie-policy max-w-[1440px] m-auto mt-16 lg:mt-20 xl:mt-28 pt-10'>
      <h1 className='text-5xl'>Cookie Policy</h1>
      <p style={{ marginTop: 16 }}>Effective Date: 09-Apr-2024</p>
      <p> Last Updated: 09-Apr-2024</p>
      &nbsp;
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        What are cookies?
      </h3>
      <div className='cookie-policy-p'>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          This Cookie Policy explains what cookies are and how we use them, the types of cookies we use i.e, the
          information we collect using cookies and how that information is used, and how to manage the cookie settings.
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          Cookies are small text files that are used to store small pieces of information. They are stored on your
          device when the website is loaded on your browser. These cookies help us make the website function properly,
          make it more secure, provide better user experience, and understand how the website performs and to analyze
          what works and where it needs improvement.
        </p>
      </div>
      &nbsp;
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        How do we use cookies?
      </h3>
      <div className='cookie-policy-p'>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          As most of the online services, our website uses first-party and third-party cookies for several purposes.
          First-party cookies are mostly necessary for the website to function the right way, and they do not collect
          any of your personally identifiable data.
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          The third-party cookies used on our website are mainly for understanding how the website performs, how you
          interact with our website, keeping our services secure, providing advertisements that are relevant to you, and
          all in all providing you with a better and improved user experience and help speed up your future interactions
          with our website.
        </p>
      </div>
      &nbsp;
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        Types of Cookies we use
      </h3>
      <div className='cky-audit-table-element'></div>
      &nbsp;
      <h3 className='big' style={{ marginBottom: 24, scrollMarginTop: 150 }}>
        Manage cookie preferences
      </h3>
      <div className='mt-4 mb-4'>
        <a
          className='cky-banner-element button primary'
          style={{
            display: 'inline-flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            borderRadius: 10,
            lineHeight: '120%',
            color: '#fff',
            cursor: 'pointer',
          }}
        >
          Cookie Settings
        </a>
      </div>
      <div>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          You can change your cookie preferences any time by clicking the above button. This will let you revisit the
          cookie consent banner and change your preferences or withdraw your consent right away.{' '}
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          In addition to this, different browsers provide different methods to block and delete cookies used by
          websites. You can change the settings of your browser to block/delete the cookies. Listed below are the links
          to the support documents on how to manage and delete cookies from the major web browsers.
        </p>{' '}
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          Chrome:{' '}
          <a href='https://support.google.com/accounts/answer/32050' target='_blank'>
            https://support.google.com/accounts/answer/32050
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          Safari:{' '}
          <a href='https://support.apple.com/en-in/guide/safari/sfri11471/mac' target='_blank'>
            https://support.apple.com/en-in/guide/safari/sfri11471/mac
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          Firefox:{' '}
          <a
            href='https://support.mozilla.org/en-US/kb/clear-cookies-and-site-data-firefox?redirectslug=delete-cookies-remove-info-websites-stored&redirectlocale=en-US'
            target='_blank'
          >
            https://support.mozilla.org/en-US/kb/clear-cookies-and-site-data-firefox?redirectslug=delete-cookies-remove-info-websites-stored&redirectlocale=en-US
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          Internet Explorer:{' '}
          <a
            href='https://support.microsoft.com/en-us/topic/how-to-delete-cookie-files-in-internet-explorer-bca9446f-d873-78de-77ba-d42645fa52fc'
            target='_blank'
          >
            https://support.microsoft.com/en-us/topic/how-to-delete-cookie-files-in-internet-explorer-bca9446f-d873-78de-77ba-d42645fa52fc
          </a>
        </p>
        <p style={{ marginBottom: 16, whiteSpace: 'pre-line' }}>
          If you are using any other web browser, please visit your browser’s official support documents.
        </p>
      </div>
      &nbsp;
      <p className='cookie-policy-p'>
        Cookie Policy Generated By{' '}
        <a target='_blank' href='https://www.cookieyes.com/?utm_source=CP&utm_medium=footer&utm_campaign=UW'>
          CookieYes - Cookie Policy Generator
        </a>
        .
      </p>
    </section>
  );
};

export default CookiePolicy;
