import Image from "next/image";
import React, { ReactNode } from "react";

type IconWithIncludedProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithIncluded: React.FC<IconWithIncludedProps> = ({
  source,
  alt,
  children,
}) => {
  return (
    <div className="bg-[#F2FAFF] rounded-[12px] py-[14px] pl-[16px] pr-[11px]">
      <div className="flex flex-row items-center gap-[14px]">
        <Image
          src={source}
          alt={alt}
          width={48}
          height={48}
          className="w-[40px] h-[40px] md:w-[48px] md:h-[48px]"
        />
        <span className="font-raleway font-medium text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] text-[#0E2432]">
          {children}
        </span>
      </div>
    </div>
  );
};

export default IconWithIncluded;
