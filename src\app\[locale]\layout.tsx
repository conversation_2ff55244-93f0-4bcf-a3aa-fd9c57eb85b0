import { headers } from 'next/headers';
import localFont from 'next/font/local';
import { notFound } from 'next/navigation';
import { getMessages, getTranslations } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import '@/app/globals.css';
import '@/sass/mobile-menu.scss';
import globalFont from '@/fonts';
import { getPrices } from '@/app/prices';
import { SessionProvider } from '@/store/SessionContext';
import { UiProvider } from '@/store/UiContext';
import { UserProvider } from '@/store/UserContext';
import AnalyticsTracker from '@/components/AnalyticsTracker';
import CompletedByNumberTimer from '@/components/ClientComponents/CompletedByNumberTimer';
import ClientLayout from '@/components/ClientLayout';
import ScriptLoader from '@/components/ScriptLoader';
import { PostHogProvider } from '@/components/PostHog/PostHogProvider';
import { Locale, isValidLocale } from '@/lib/i18n/locales';
import { getSiteConfig } from '../../../site.config';

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  const siteConfig = getSiteConfig(headers().get('x-vercel-ip-country'));
  const t = await getTranslations({ locale, namespace: 'site_metadata' });

  return {
    title: `${siteConfig.siteName} - ${t('title')}`,
    description: t('description'),
    icons: {
      icon: '/favicon.ico',
    },
  };
}

const ppMori = localFont({
  src: [
    {
      path: '../../fonts/PPMori-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../fonts/PPMori-SemiBold.woff2',
      weight: '600',
      style: 'normal',
    },
  ],
});

export default async function RootLayout({
  children,
  params: { locale }
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  // Type-safe locale validation using imported utility
  if (!isValidLocale(locale)) {
    notFound();
  }

  const messages = await getMessages();
  const siteConfig = getSiteConfig(headers().get('x-vercel-ip-country'));

  return (
    <html lang="en">
      <head>
        <link rel="apple-touch-icon" sizes="57x57" href="/apple-icon-57x57.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#ffffff" />
        <ScriptLoader />
        <AnalyticsTracker siteConfig={siteConfig} />
      </head>
      <body className={`bg-[url('/home/<USER>/bg-mobile.svg')] md:bg-[url('/home/<USER>/bg-desktop.svg')] bg-cover bg-top ${globalFont.className}`}>
        <NextIntlClientProvider messages={messages}>
          <PostHogProvider>
            <SessionProvider siteConfig={siteConfig} prices={await getPrices()}>
              <UserProvider>
                <UiProvider>
                  <ClientLayout>{children}</ClientLayout>
                  <CompletedByNumberTimer />
                </UiProvider>
              </UserProvider>
            </SessionProvider>
          </PostHogProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
