/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  Es = 'es',
  Fr = 'fr',
  De = 'de',
  Pt = 'pt',
  El = 'el'
}

/** All available locales */
const allLocales: string[] = Object.values(Locale);

/** Environment-based locale configuration */
const isMultiLanguageEnabled = process.env.ENABLE_MULTI_LANGUAGE === 'true';

/** List of supported locales based on environment */
export const locales: string[] = isMultiLanguageEnabled ? allLocales : [Locale.En];

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = Locale.En;

/** The country that will be used when no match is found. */
export const defaultCountry: string = 'US';
