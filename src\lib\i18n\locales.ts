/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  Es = 'es',
  Fr = 'fr',
  De = 'de',
  Pt = 'pt',
  El = 'el'
}

/** All available locales */
const allLocales: string[] = Object.values(Locale);

/** Environment-based locale configuration utility */
export const isMultiLanguageEnabled = process.env.NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE === 'true';

/** Get default locale from environment variable with fallback */
const getDefaultLocale = (): Locale => {
  const defaultLocale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE;
  // Validate that the environment locale is a valid Locale enum value
  if (defaultLocale && Object.values(Locale).includes(defaultLocale as Locale)) {
    return defaultLocale as Locale;
  }
  // Fallback to English if invalid or not set
  return Locale.En;
};

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = getDefaultLocale();

/** List of supported locales based on environment */
export const locales: string[] = isMultiLanguageEnabled ? allLocales : [defaultLocale];

/** The country that will be used when no match is found. */
export const defaultCountry: string = 'US';
