/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  Es = 'es',
  Fr = 'fr',
  De = 'de',
  Pt = 'pt',
  El = 'el'
}

/** All available locales */
const allLocales: string[] = Object.values(Locale);

/** Environment-based locale configuration utility */
export const isMultiLanguageEnabled = process.env.NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE === 'true';

/** Helper function to check if a value is a valid locale */
export const isValidLocale = (value: string): value is Locale => {
  return Object.values(Locale).includes(value as Locale);
};

/** Language configuration interface */
export interface Language {
  code: Locale;
  name: string;
  flag: string;
}

/** Complete language configuration with display names and flags */
export const languages: Language[] = [
  { code: Locale.En, name: 'English', flag: '🇺🇸' },
  { code: Locale.De, name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: Locale.Es, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  { code: Locale.Fr, name: 'Fran<PERSON>', flag: '🇫🇷' },
  { code: Locale.Pt, name: 'Português', flag: '🇵🇹' },
  { code: Locale.El, name: 'Ελληνικά', flag: '🇬🇷' },
];

/** Get language configuration by locale code */
export const getLanguageByCode = (code: string): Language | undefined => {
  return languages.find(lang => lang.code === code);
};

/** Get available languages based on environment configuration */
export const getAvailableLanguages = (): Language[] => {
  if (!isMultiLanguageEnabled) {
    // Return only the default language when multi-language is disabled
    const defaultLang = getLanguageByCode(defaultLocale);
    return defaultLang ? [defaultLang] : [languages[0]];
  }
  return languages;
};

/** Get default locale from environment variable with fallback */
const getDefaultLocale = (): Locale => {
  const envLocale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE;
  // Use the centralized validation utility
  if (envLocale && isValidLocale(envLocale)) {
    return envLocale;
  }
  // Fallback to English if invalid or not set
  return Locale.En;
};

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = getDefaultLocale();

/** List of supported locales based on environment */
export const locales: string[] = isMultiLanguageEnabled ? allLocales : [defaultLocale];

/** The country that will be used when no match is found. */
export const defaultCountry: string = 'US';
