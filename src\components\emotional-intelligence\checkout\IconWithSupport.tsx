import Image from "next/image";
import React, { ReactNode } from "react";

type IconWithSupportProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithSupport: React.FC<IconWithSupportProps> = ({
  source,
  alt,
  children,
}) => {
  return (
    <div className="flex flex-row items-center py-[8px] px-[12px] md:px-[16px] gap-2 bg-[#F6F6F6] rounded-[8px]">
      <Image
        src={source}
        alt={alt}
        width={18}
        height={18}
        className="w-[16px] md:w-[18px] h-[16px] md:h-[18px]"
      />
      <span
        className="font-ppmori font-normal text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] text-[#0C0113]"
        style={{
          fontFeatureSettings: "'pnum' on, 'lnum' on",
        }}
      >
        {children}
      </span>
    </div>
  );
};

export default IconWithSupport;
