import React, { useState, useEffect } from 'react';
import NumberWithChallenge from '@/components/love-languages/NumberWithChallenge';
import { personalgrowthchallenge } from '@/app/[locale]/love-languages/results/PersonalGrowthChallenge';

type PersonalGrowthChallengeProps = {
  category: string;
};

const PersonalGrowthChallenge: React.FC<PersonalGrowthChallengeProps> = ({ category }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const updateIsMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(!window.matchMedia('(min-width: 768px)').matches);
      }
    };

    updateIsMobile(); // Initial check on mount
    window.addEventListener('resize', updateIsMobile);

    return () => {
      window.removeEventListener('resize', updateIsMobile);
    };
  }, []);

  return (
    <div className="flex flex-col gap-6 md:gap-[28px]">
      <NumberWithChallenge
        sequence={1}
        source={
          isMobile ? '/images/love-languages/result-support-mobile.png' : '/images/love-languages/result-support.png'
        }
        alt="Result Support"
        heading={personalgrowthchallenge[category] && personalgrowthchallenge[category][0].title}>
        {personalgrowthchallenge[category] && personalgrowthchallenge[category][0].description}
      </NumberWithChallenge>
      <NumberWithChallenge
        sequence={2}
        source={
          isMobile
            ? '/images/love-languages/result-communicate-mobile.png'
            : '/images/love-languages/result-communicate.png'
        }
        alt="Result Communicate"
        heading={personalgrowthchallenge[category] && personalgrowthchallenge[category][1].title}>
        {personalgrowthchallenge[category] && personalgrowthchallenge[category][1].description}
      </NumberWithChallenge>
      <NumberWithChallenge
        sequence={3}
        source={
          isMobile
            ? '/images/love-languages/result-gratitude-mobile.png'
            : '/images/love-languages/result-gratitude.png'
        }
        alt="Result Gratitude"
        heading={personalgrowthchallenge[category] && personalgrowthchallenge[category][2].title}>
        {personalgrowthchallenge[category] && personalgrowthchallenge[category][2].description}
      </NumberWithChallenge>
      <NumberWithChallenge
        sequence={4}
        source={
          isMobile
            ? '/images/love-languages/result-highlight-mobile.png'
            : '/images/love-languages/result-highlight.png'
        }
        alt="Result Highlight"
        heading={personalgrowthchallenge[category] && personalgrowthchallenge[category][3].title}>
        {personalgrowthchallenge[category] && personalgrowthchallenge[category][3].description}
      </NumberWithChallenge>
      <NumberWithChallenge
        sequence={5}
        source={
          isMobile ? '/images/love-languages/result-connect-mobile.png' : '/images/love-languages/result-connect.png'
        }
        alt="Result Connect"
        heading={personalgrowthchallenge[category] && personalgrowthchallenge[category][4].title}>
        {personalgrowthchallenge[category] && personalgrowthchallenge[category][4].description}
      </NumberWithChallenge>
    </div>
  );
};

export default PersonalGrowthChallenge;
