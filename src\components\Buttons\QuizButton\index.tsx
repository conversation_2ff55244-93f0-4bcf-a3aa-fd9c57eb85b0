'use client';

import { useContext } from 'react';
import type { FC } from 'react';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useRouter } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';
import { PostHogEventEnum } from '@/store/types';

interface QuizButtonProps {
  className?: string;
  type: 'back' | 'next' | 'done' | 'skip';
  setVisible?: any;
  label?: string;
}

const QuizButton: FC<QuizButtonProps> = ({ className, type, setVisible, label }) => {
  const router = useRouter();
  const { captureEvent } = usePostHogAnalytics();
  const { questionId, updateQuestionId, answers } = useContext(SessionContext);

  const handleClick = (e: any) => {
    const isQuizDone = answers.length === 30 && answers.filter(answer => answer.answerId > 0).length === 30;
    switch (type) {
      case 'back':
        updateQuestionId(questionId - 1);
        captureEvent(PostHogEventEnum.BACK_TO_PREVIOUS_QUESTION, {
          fromQuestionId: questionId,
          toQuestionId: questionId - 1,
        });
        break;
      case 'skip':
        updateQuestionId(questionId + 1);
        captureEvent(PostHogEventEnum.QUESTION_SKIPPED, {
          questionId,
          skippedToQuestionId: questionId + 1,
        });
        break;
      case 'done':
        if (questionId === 30) {
          captureEvent(PostHogEventEnum.GET_RESULTS_CLICKED, {});
          if (isQuizDone === false) {
            e.preventDefault();
            setVisible?.(true);
          } else {
            router.push('/calculation');
          }
        }
      default:
        break;
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`button primaryColor ${
        className ? className : ''
      } font-semibold inline-flex py-3 px-[18px] xxs:py-4 xxs:px-7 xs:px-10 justify-end items-center mr-2 mb-2 2xl:mr-[18px] border border-primary 
        ${type === 'back' ? 'lgh:ml-0 lg:ml-[92px] xl:!ml-[135px]' : 'lgh:mr-[5px] lg:mr-[98px] xl:!mr-[147px]'}
        ${['next', 'done'].includes(type) ? 'text-white bg-primary' : 'text-primary bg-white'}
      `}
      style={{
        borderRadius: 10,
        lineHeight: '120%',
      }}>
      {label || (type === 'done' ? 'Get the Results' : type.charAt(0).toUpperCase() + type.slice(1))}
    </button>
  );
};

export default QuizButton;
