import Image from "next/image";
import React, { ReactNode } from "react";

type IconWithIncludedProps = {
  source: string;
  alt: string;
  children?: ReactNode;
};

const IconWithIncluded: React.FC<IconWithIncludedProps> = ({
  source,
  alt,
  children,
}) => {
  return (
    <div className="w-full bg-[#FBF6FF] rounded-[12px] p-[12px] md:p-[14px]">
      <div className="flex flex-row md:flex-col w-full md:max-w-[168px] gap-[12px]">
        <Image
          src={source}
          alt={alt}
          width={48}
          height={48}
          className="w-[40px] h-[40px] md:w-[48px] md:h-[48px]"
        />
        <span className="font-ppmori font-normal text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] my-auto text-[#0C0113]">
          {children}
        </span>
      </div>
    </div>
  );
};

export default IconWithIncluded;
