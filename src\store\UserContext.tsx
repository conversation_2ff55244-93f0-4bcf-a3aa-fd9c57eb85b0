'use client';

import { createContext, FC, ReactNode, Suspense, useEffect, useState } from 'react';
import { httpsCallable } from 'firebase/functions';
import { auth, functions } from '@/utils/firebase';
import { useRouter } from '@/lib/i18n/navigation';

interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  is_subscribed: boolean;
  current_period_end: any;
  scheduled_cancel_at: any;
  activeSubscriptionType: 'stripe' | 'solidgate';
}

interface UserContextProps {
  user: User | null | undefined;
  logout: () => void;
  trainingQuestions: {
    [prop: string]: {
      id: number;
      originalQuestionId: number;
      correctAnswerId: number;
      category: string;
      explanation: string;
      numberOfAnswers: number;
    }[];
  };
}

const UserContext = createContext<UserContextProps>({ user: null, logout: () => {}, trainingQuestions: {} });

const UserProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null | undefined>(undefined);
  const [trainingQuestions, setTrainingQuestions] = useState({});
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  async function getTrainingQuestions() {
    const getQuestions = httpsCallable(functions, 'getTrainingQuestions');
    const result: any = await getQuestions();
    setTrainingQuestions(result.data.questions);
  }

  const parseDateFormat = (date: string | number, debug?: boolean) => {
    if (date === null) {
      return null;
    }

    const date1 = new Date((date as number) * 1000);
    const date2 = new Date(date);

    const finalDate = isNaN(date1.getTime()) ? date2 : date1;

    return !isNaN(finalDate.getTime()) ? finalDate : null;
  };

  const updateUser = async (firebaseUser: any) => {
    if (firebaseUser) {
      const claims = (await firebaseUser.getIdTokenResult()).claims;

      let user = {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        current_period_end: parseDateFormat(claims.current_period_end),
        scheduled_cancel_at: parseDateFormat(claims.scheduled_cancel_at, true),
        is_subscribed: claims.is_subscribed as boolean,
        activeSubscriptionType: claims.activeSubscriptionType,
      };

      if (user.is_subscribed) getTrainingQuestions();

      localStorage.setItem('user', JSON.stringify(user));
      setUser(user);
    } else {
      setUser(null);
      localStorage.removeItem('user');
    }
  };

  useEffect(() => {
    const userLocalStorage = localStorage.getItem('user');
    if (userLocalStorage) setUser(JSON.parse(userLocalStorage));

    setLoading(false);

    const unsubscribeIdTokenChange = auth.onIdTokenChanged(updateUser);
    const unsubscribeAuthState = auth.onAuthStateChanged(updateUser);

    return () => {
      unsubscribeIdTokenChange();
      unsubscribeAuthState();
    };
  }, []);

  const logout = async () => {
    await auth.signOut();
    localStorage.clear();
    router.push('/');
  };

  return (
    <UserContext.Provider value={{ user, logout, trainingQuestions }}>
      <Suspense>{children}</Suspense>
    </UserContext.Provider>
  );
};

export { UserContext, UserProvider };
