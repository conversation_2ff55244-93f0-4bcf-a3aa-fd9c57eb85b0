import Footer from '@/components/Footer';
import MobileMenu from '@/components/MobileMenu';

export default function ComplexLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="w-full">
      <div className="w-full flex flex-col">
        <div className="w-full bg-[#EBF8FF]">
          <div className="max-w-[1120px] mx-auto py-[17.5px] md:py-[27px] text-[#0D0D0E] font-ppmori font-semibold text-[25.8px] md:text-[31.15px] leading-[48px] text-center md:text-left">
            IQ International Support
          </div>
        </div>
        {children}
      </div>
      <MobileMenu />
    </div>
  );
}
