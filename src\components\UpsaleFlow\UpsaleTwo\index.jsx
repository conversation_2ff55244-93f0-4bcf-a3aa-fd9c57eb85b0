import Image from 'next/image';
import UpsalePath from '@/components/UpsaleFlow/UpselPath';

export default function UpsaleTwo({ handleNext }) {
  return (
    <div>
      <div className="container mx-auto p-2 md:p-0">
        <div className="m-auto hidden md:flex rounded-2xl justify-center content-center md:w-[500px] bg-[#D9F0FF] my-10 mt-3 md:mt-8">
          <div className="p-5 flex-none w-16">
            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_2414_4306)">
                <path
                  d="M25.7202 5.73149C21.6807 9.34515 16.9853 12.1502 11.8887 13.9946L10.5967 14.4649C8.32613 15.2876 6.44783 16.9338 5.33446 19.0768C4.22109 21.2198 3.954 23.7031 4.5862 26.0339C5.21841 28.3646 6.70369 30.3726 8.74735 31.6593C10.791 32.946 13.2437 33.4174 15.6187 32.98C17.0385 35.1064 18.6609 37.0902 20.4632 38.9037C21.8922 40.3424 24.1749 40.1281 25.4106 38.656L26.3048 37.5911C27.5023 36.1641 27.3129 34.1162 26.0872 32.8552C25.3512 32.0993 24.6538 31.3067 23.9977 30.4805C27.4637 29.7223 30.9456 29.4149 34.3796 29.5229C34.2646 25.2216 33.4535 20.9674 31.9777 16.9256C30.464 12.7668 28.3316 9.01467 25.7202 5.73149ZM27.7166 3.82453C30.6517 7.47389 32.9645 11.5826 34.5618 15.985C36.276 20.6797 37.1501 25.6396 37.144 30.6375C37.1437 30.8181 37.1789 30.9971 37.2477 31.1641C37.3165 31.3311 37.4175 31.4829 37.545 31.6109C37.6724 31.7389 37.8239 31.8405 37.9906 31.91C38.1573 31.9794 38.3361 32.0153 38.5168 32.0157C38.6974 32.0161 38.8763 31.9808 39.0433 31.912C39.2104 31.8432 39.3622 31.7422 39.4902 31.6148C39.6181 31.4873 39.7198 31.3359 39.7892 31.1691C39.8587 31.0024 39.8946 30.8236 39.895 30.643C39.9031 26.6013 39.3669 22.5769 38.3008 18.6783C38.8762 17.3796 38.9254 15.9084 38.4381 14.5742C37.9321 13.1839 36.9229 12.1211 35.6949 11.5185C34.0037 7.84773 31.8278 4.42045 29.2253 1.32831C29.1089 1.19017 28.9665 1.0763 28.8061 0.993214C28.6457 0.910126 28.4705 0.859443 28.2905 0.844057C27.9271 0.812983 27.5661 0.927571 27.2872 1.16261C27.0082 1.39765 26.834 1.73389 26.8029 2.09736C26.7718 2.46084 26.8864 2.82176 27.1215 3.10075C27.3234 3.3394 27.522 3.5793 27.7166 3.82453Z"
                  class="fill-primary"
                />
              </g>
              <defs>
                <clipPath id="clip0_2414_4306">
                  <rect width="44" height="44" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div className="flex-1 py-3 px-4">
            <h4 className="text-[16px]">Caution!</h4>
            <p className="text-[#191919B2] text-[14px]">
              To prevent double charges please don’t close the page and don’t go back
            </p>
          </div>
        </div>
        <UpsalePath />
        <div className="text-center text-[52px] text-[#191919] pt-[40px] md:pt-[40px]">
          <h1 className="text-[26px] md:text-[36px] pt-2 md:pb-1">Are you sure?</h1>
          <p className="md:max-w-[600px] text-[13px] md:text-[16px] mx-auto">
            Supercharge your brain in <b className="text-black">4 weeks</b> — sign up today for only{' '}
            <b className="text-black">$1.78/day</b>, limited-time introduction offer!
          </p>
          <h3 className="pt-2 text-[20px] md:text-[24px] md:pt-4">What’s included</h3>
        </div>
        <div className="md:flex justify-between max-w-[840px] mx-auto mb-3">
          <div className="md:w-[490px] p-2 md:p-5 bg-white shadow mt-2 md:mt-3 mx-auto">
            <ul>
              <li className="text-[16px] flex mb-1 md:mb-4">
                <div>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" class="fill-primary" />
                    <path
                      d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <p className="ml-3 text-[#191919A6] text-[15px]">
                  A detailed <b className="text-black">IQ score</b> using our validated testing method
                </p>
              </li>
              <li className="text-[16px] flex mb-4 mb-1 md:mb-4">
                <div>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" class="fill-primary" />
                    <path
                      d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <p className="ml-3 text-[#191919A6] text-[15px]">
                  A <b className="text-black">comparison</b> of your <b className="text-black">results</b> with the
                  general population
                </p>
              </li>
              <li className="text-[16px] flex mb-1 md:mb-4">
                <div>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" class="fill-primary" />
                    <path
                      d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <p className="ml-3 text-[#191919A6] text-[15px]">
                  An overview of your cognitive abilities, highlighting areas of <b className="text-black">strength</b>{' '}
                  and <b className="text-black">improvement</b>
                </p>
              </li>
              <li className="text-[16px] flex">
                <div>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" class="fill-primary" />
                    <path
                      d="M14.114 6.27036C14.5455 5.90988 15.245 5.90988 15.6764 6.27036C16.1026 6.62643 16.1078 7.201 15.6921 7.56243L9.81144 13.7043C9.80296 13.7131 9.79389 13.7216 9.78428 13.7296C9.35284 14.0901 8.65333 14.0901 8.22189 13.7296L4.32358 10.4725C3.89214 10.112 3.89214 9.52753 4.32358 9.16704C4.75502 8.80656 5.45453 8.80656 5.88597 9.16704L8.9698 11.7437L14.0847 6.29798C14.0938 6.28829 14.1036 6.27907 14.114 6.27036Z"
                      fill="white"
                    />
                  </svg>
                </div>
                <p className="ml-3 text-[#191919A6] text-[15px]">
                  A training plan designed to <b className="text-black">increase your IQ</b> by up to{' '}
                  <b className="text-black">38%</b> over the next 4 weeks
                </p>
              </li>
            </ul>
          </div>
        </div>
        <h4 className="text-primary text-[20px] text-center mb-2 md:mb-8">Don’t miss this—limited-time!</h4>
        <div className="flex border-t-2 justify-between">
          <button
            onClick={() => {
              handleNext();
            }}
            className="px-8 w-[32%] mx-[1%] md:w-auto py-3 bg-[#E8EDF8] text-black rounded-md my-6">
            Skip
          </button>
          <button
            onClick={() => {
              handleNext();
            }}
            className="text-white w-[66%] mx-[1%] md:w-[300px] px-8 py-3 bg-primary rounded-md my-6">
            Accept offer and Continue
          </button>
        </div>
      </div>
    </div>
  );
}
