'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

const SupportSection = () => {
  const t = useTranslations('footer');

  return (
    <div className="flex flex-col font-semibold text-[18px] leading-[27px] tracking-[0] text-[#000]">
      <Link href="/support" className="mb-[15.5px]">
        {t('support.title')}
      </Link>
      <span className="mb-[12.5px]">{t('support.need_help')}</span>
      <div className="border-2 border-gray-200 rounded-[10px] py-[10px] pl-[18.91px] pr-[17.62px] w-fit">
        <Link href="/support" className="flex flex-row gap-[11.95px]">
          <Image
            src="/images/footer/receiver.svg"
            alt="receiver"
            width={25}
            height={25}
            quality={100}
            priority
          />
          <div className="flex flex-col text-[14px] leading-[21px] text-left">
            <span>{t('support.customer_support.title')}</span>
            <span>{t('support.customer_support.availability')}</span>
          </div>
        </Link>
      </div>
    </div>
  );
};

export default SupportSection;
