// import { storage } from '@/utils/firebase';
// import { getDownloadURL, ref } from 'firebase/storage';
import Image from 'next/image';
// import { useEffect, useState } from 'react';

const TrainingQuestionsImage = ({ questionId }: { questionId: number }) => {
  // const [imageUrl, setImageUrl] = useState<string | null>(null);

  // useEffect(() => {
  //   const fetchImageUrl = async () => {
  //     try {
  //       const imageRef = ref(storage, `training-questions/${questionId}/question.svg`);
  //       const url = await getDownloadURL(imageRef);
  //       setImageUrl(url);
  //     } catch (error) {
  //       console.error('Error fetching image URL:', error);
  //     }
  //   };

  //   fetchImageUrl();
  // }, [questionId]);

  return (
      <Image
        src={`/training-questions/${questionId}/question.svg`}
        alt={`Image of IQ test question.`}
        width={500}
        height={500}
        style={{
          border: `2px solid transparent`,
          boxShadow:
            '-3.563px 7.126px 12.471px 0px rgba(104, 129, 177, 0.08), -0.891px 0.891px 0.891px 0.891px rgba(141, 160, 188, 0.08)',
        }}
        className='h-auto'
        unoptimized
        priority
      />
  )
};

export default TrainingQuestionsImage;
