import { functions } from "@/utils/firebase";
import { httpsCallable } from "firebase/functions";

export const addFormDataToSessionDb = async (sessionData: any) => {
  try {
    const saveSession = httpsCallable(functions, 'saveSession');
    const response:any = await saveSession(sessionData);
    if (!response.data.success) {
      throw new Error('Save session data error.');
    }
    return true;
  } catch (e: any) {
    console.info(e);
    return false;
  }
};
