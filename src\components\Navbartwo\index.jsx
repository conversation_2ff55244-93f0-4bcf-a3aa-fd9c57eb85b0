'use client'; // Enable client-side functionality

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import Image from 'next/image';
import JustBoughtFunction from './just_bought';

export default function Navbartwo({ name, image, IQ }) {
  const [mounted, setMounted] = useState(false);
  const [container, setContainer] = useState(null);
  const [displayContent, setDisplayContent] = useState({ name, image, IQ });
  const [fade, setFade] = useState(false); // State for controlling fade

  useEffect(() => {
    // Ensure that `document.body` exists and create a container
    if (typeof document !== 'undefined' && document.body) {
      const div = document.createElement('div'); // Create a new div
      document.body.insertBefore(div, document.body.firstChild); // Insert div at the beginning of body
      setContainer(div); // Save the div as a container
      setMounted(true);
    }

    return () => {
      if (container) {
        document.body.removeChild(container); // Clean up by removing the div on unmount
      }
    };
  }, []);

  useEffect(() => {
    if (!mounted) return;

    // Fade out before updating content
    setFade(true); // Start fading out
    const timeout = setTimeout(() => {
      setDisplayContent({ name, image, IQ }); // Update content
      setFade(false); // Fade back in
    }, 500); // Match the timeout duration to the transition duration

    return () => clearTimeout(timeout); // Cleanup timeout on unmount
  }, [name, image, IQ, mounted]);

  if (!mounted || !container) return null;

  // This will append content to the created `div` at the start of the `body`
  return createPortal(
    <div className={`h-[40px] md:h-[60px]`}>
      <div className="border-b-2 fixed top-0 left-0 w-full bg-white z-[1000]">
        <div
          className={`flex transition-opacity duration-500 ease-in-out justify-center ${
            fade ? 'opacity-0' : 'opacity-100'
          } md:text-[18px] w-full md:w-[450px] mx-auto text-center py-2 md:py-4`}>
          <p className="pt-2 md:pt-1 text-[12px] md:text-[16px] text-[#191919]">
            <b>{displayContent.name}</b> Just Bought his IQ Score!
          </p>
          <img src={displayContent.image} className="mx-2 h-[16px] mt-[7px] w-[30px]" alt={`flag`} />
          <p className="text-primary text-[14px] md:text-[16px] pt-1.5 md:pt-1 font-[600]"> IQ {displayContent.IQ}</p>
        </div>
      </div>
    </div>,
    container // Append it to the newly created container
  );
}
