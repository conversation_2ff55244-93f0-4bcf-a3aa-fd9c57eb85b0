/* ------------------------------------------------------------------
 * Global Edge middleware
 * -----------------------------------------------------------------*/
import { NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from '@/lib/i18n/routing';
import { defaultCountry, defaultLocale, Locale, isMultiLanguageEnabled, isValidLocale } from '@/lib/i18n/locales';

// Create the next-intl handler
const handleI18nRouting = createMiddleware(routing);

/* ------------------------------------------------------------------
* Only run for “real” pages – skip static assets etc.
* -----------------------------------------------------------------*/
export const config = {
  // Match all pages except API routes, static files, and Next.js internals
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)', '/']
};

/**
 * Middleware to enhance incoming requests with client IP and location info.
 *
 * - Sets `x-ip` header with the client IP.
 * - Persists detected country code in a `locale` cookie.
 * - Handles language detection based on environment configuration.
 *
 * @param req - Incoming request handled by Next.js Edge Middleware.
 * @returns Modified NextResponse with enriched headers and cookies.
 */
export function middleware(req: NextRequest): NextResponse {
  const ip = req.ip ?? '';
  const country = req.geo?.country ?? defaultCountry;

  // Default to environment-configured locale
  let detectedLanguage = defaultLocale; 

  // Multi-language is enabled via imported utility
  if (isMultiLanguageEnabled) {
    // Multi-language detection logic for staging/development
    const languageFromCookie = req.cookies.get('preferred-language')?.value;
    const acceptLanguage = req.headers.get('accept-language');

    if (languageFromCookie && isValidLocale(languageFromCookie)) {
      detectedLanguage = languageFromCookie as Locale;
    } else if (acceptLanguage) {
      // Simple language detection from Accept-Language header
      if (acceptLanguage.includes(Locale.De)) detectedLanguage = Locale.De;
      else if (acceptLanguage.includes(Locale.Es)) detectedLanguage = Locale.Es;
      else if (acceptLanguage.includes(Locale.Fr)) detectedLanguage = Locale.Fr;
      else if (acceptLanguage.includes(Locale.Pt)) detectedLanguage = Locale.Pt;
      else if (acceptLanguage.includes(Locale.El)) detectedLanguage = Locale.El;
    }
  }

  // Set the headers for the request
  const headers = new Headers(req.headers);
  headers.set('x-ip', ip);
  headers.set('x-locale', detectedLanguage);

  const response = handleI18nRouting(new NextRequest(req.nextUrl, { headers }));

  // Set the country and preferred language cookies
  response.cookies.set('locale', country);
  response.cookies.set('preferred-language', detectedLanguage);

  // Set the headers for the response
  response.headers.set('x-ip', ip);
  response.headers.set('x-locale', detectedLanguage);

  return response;
}