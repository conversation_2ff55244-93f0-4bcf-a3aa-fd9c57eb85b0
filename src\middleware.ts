/* ------------------------------------------------------------------
 * Global Edge middleware
 * -----------------------------------------------------------------*/
import { NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from '@/lib/i18n/routing';
import { defaultCountry } from '@/lib/i18n/locales';

// Create the next-intl handler
const handleI18nRouting = createMiddleware(routing);

/* ------------------------------------------------------------------
* Only run for “real” pages – skip static assets etc.
* -----------------------------------------------------------------*/
export const config = {
  // Match all pages except API routes, static files, and Next.js internals
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)', '/', '/(de|fr|es|pt|hr|el)/:path*']
};

/**
 * Middleware to enhance incoming requests with client IP and location info.
 *
 * - Sets `x-ip` header with the client IP.
 * - Persists detected country code in a `locale` cookie.
 *
 * @param req - Incoming request handled by Next.js Edge Middleware.
 * @returns Modified NextResponse with enriched headers and cookies.
 */
export function middleware(req: NextRequest): NextResponse {
  /* ----- 1.  Gather information ----------------------------------- */
  const ip = req.ip ?? '';
  const country = req.geo?.country ?? defaultCountry;

  /* ----- 2.  Enrich the *request* headers with the client IP ------- */
  const headers = new Headers(req.headers);
  headers.set('x-ip', ip);

  /* ----- 3.  Let next-intl decide about locale-prefixed routes ----- */
  const response = handleI18nRouting(new NextRequest(req.nextUrl, { headers }));

  /* ----- 4.  Persist data & expose IP on the *response* ------------ */
  response.cookies.set('locale', country);
  response.headers.set('x-ip', ip);

  // Return the modified response
  return response;
}