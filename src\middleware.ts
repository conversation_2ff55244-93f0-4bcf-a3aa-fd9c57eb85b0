/* ------------------------------------------------------------------
 * Global Edge middleware
 * -----------------------------------------------------------------*/
import { NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from '@/lib/i18n/routing';
import { defaultCountry, Locale, defaultLocale } from '@/lib/i18n/locales';

// Create the next-intl handler
const handleI18nRouting = createMiddleware(routing);

/* ------------------------------------------------------------------
* Only run for “real” pages – skip static assets etc.
* -----------------------------------------------------------------*/
export const config = {
  // Match all pages except API routes, static files, and Next.js internals
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)', '/']
};

/**
 * Middleware to enhance incoming requests with client IP and location info.
 *
 * - Sets `x-ip` header with the client IP.
 * - Persists detected country code in a `locale` cookie.
 * - Detects user's preferred language and sets it in headers.
 *
 * @param req - Incoming request handled by Next.js Edge Middleware.
 * @returns Modified NextResponse with enriched headers and cookies.
 */
export function middleware(req: NextRequest): NextResponse {
  /* ----- 1.  Gather information ----------------------------------- */
  const ip = req.ip ?? '';
  const country = req.geo?.country ?? defaultCountry;

  /* ----- 2.  Force English language only (temporarily) --------- */
  // For now, only English is enabled - ignore user preferences and browser settings
  const detectedLanguage = defaultLocale; // Always use English

  // TODO: Re-enable multi-language support later by uncommenting below:
  /*
  // Check for language preference in cookie first
  const languageFromCookie = req.cookies.get('preferred-language')?.value;

  // Check Accept-Language header as fallback
  const acceptLanguage = req.headers.get('accept-language');
  let detectedLanguage = defaultLocale; // default

  // Helper function to check if a value is a valid locale
  const isValidLocale = (value: string): value is Locale => {
    return Object.values(Locale).includes(value as Locale);
  };

  if (languageFromCookie && isValidLocale(languageFromCookie)) {
    detectedLanguage = languageFromCookie as Locale;
  } else if (acceptLanguage) {
    // Simple language detection from Accept-Language header
    if (acceptLanguage.includes(Locale.De)) detectedLanguage = Locale.De;
    else if (acceptLanguage.includes(Locale.Es)) detectedLanguage = Locale.Es;
    else if (acceptLanguage.includes(Locale.Fr)) detectedLanguage = Locale.Fr;
    else if (acceptLanguage.includes(Locale.Pt)) detectedLanguage = Locale.Pt;
    else if (acceptLanguage.includes(Locale.El)) detectedLanguage = Locale.El;
  }
  */

  /* ----- 3.  Enrich the *request* headers with the client IP and language ------- */
  const headers = new Headers(req.headers);
  headers.set('x-ip', ip);
  headers.set('x-locale', detectedLanguage);

  /* ----- 4.  Let next-intl decide about locale-prefixed routes ----- */
  const response = handleI18nRouting(new NextRequest(req.nextUrl, { headers }));

  /* ----- 5.  Persist data & expose IP on the *response* ------------ */
  response.cookies.set('locale', country);
  response.cookies.set('preferred-language', detectedLanguage);
  response.headers.set('x-ip', ip);
  response.headers.set('x-locale', detectedLanguage);

  // Return the modified response
  return response;
}