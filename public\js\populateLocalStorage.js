(() => {
  function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
  }

  function tryToPopulateLocalStorage () {
    const urlQuery = new URLSearchParams(window.location.search);
    if (
      getCookie('cookieyes-consent')
        ?.split(',')
        .filter((i) => i.includes('analytics'))?.[0]
        ?.split(':')[1] === 'yes'
    ) {
      const clickId = urlQuery.get('click_id');
      
      if (!localStorage.getItem('landingUrl') || localStorage.getItem('landingUrl')  == 'null' || !localStorage.getItem('landingUrl').includes('click_id'))
        localStorage.setItem('landingUrl', window.location.href);
      
      if (!localStorage.getItem('lscache-se-urlQuery') || localStorage.getItem('lscache-se-urlQuery') == 'null') {
        localStorage.setItem('lscache-se-urlQuery', urlQuery);
        localStorage.setItem('lscache-se-urlQuery-cacheexpiration', 29105056);
      }
      
      if (!localStorage.getItem('lscache-se-clickId') || localStorage.getItem('lscache-se-clickId') == 'null') {
        localStorage.setItem('lscache-se-clickId', clickId);
        localStorage.setItem('lscache-se-clickId-cacheexpiration', 29105056);
      }
    }

    // Determine checkout version based on presence of 'fw2' query param
    const checkoutVersion = urlQuery.get('fw2') ? 'v2' : 'v1';
    // Always override the value in localStorage
    localStorage.setItem('checkoutVersion', checkoutVersion);
  }

  document.addEventListener("cookieyes_consent_update", function (eventData) 
  {
    tryToPopulateLocalStorage();
  });

  tryToPopulateLocalStorage();
})();