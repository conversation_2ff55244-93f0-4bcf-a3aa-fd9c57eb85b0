'use client';

import { memo, useContext, useEffect } from 'react';
import { httpsCallable } from 'firebase/functions';
import { useSearchParams } from 'next/navigation';
import { usePostHogAnalytics } from '@/hooks/useAnalytics';
import { useRouter } from '@/lib/i18n/navigation';
import { functions } from '@/utils/firebase';
import QuestionCategoryTag from '@/components/Tags/QuestionCategoryTag';
import SessionContext from '@/store/SessionContext';
import { UserContext } from '@/store/UserContext';
import { PostHogEventEnum } from '@/store/types';
import { useTranslations } from 'next-intl';

const Calculation = () => {
  const t = useTranslations('calculation');
  const router = useRouter();
  const { captureEvent } = usePostHogAnalytics();
  const { user } = useContext(UserContext);
  const { answers, updateSessionId } = useContext(SessionContext);
  const searchParams = useSearchParams();
  const showResults = searchParams.get('showResults');
  const origin = searchParams.get('origin');

  const tags = [
    t('intelligence_aspects.memory'),
    t('intelligence_aspects.reaction'),
    t('intelligence_aspects.logic'),
    t('intelligence_aspects.concentration'),
    t('intelligence_aspects.speed')
  ];

  async function generateReportAndCertificate() {
    const generate = httpsCallable(functions, 'generateReportAndCertificate');
    const result = await generate({ answers, showResults });
    return result;
  }

  useEffect(() => {
    if (user && !user.is_subscribed) return;

    const promises = [new Promise(res => setTimeout(res, 7 * 1000))];
    if (user && user.is_subscribed)
      promises.push(generateReportAndCertificate().then((x: any) => updateSessionId(x.data.sessionId)));

    Promise.all(promises).then(x => router.push(user?.is_subscribed ? '/results' : '/form?origin=calculation'));
  }, []);

  useEffect(() => {
    if (origin === 'questions') {
      captureEvent(PostHogEventEnum.CALCULATION_PAGE_VIEWED, {});
    }
    // eslint-disable-next-line
  }, [origin]);

  if (user && !user.is_subscribed) {
    return (
      <div className="w-full items-center flex flex-col">
        <h5 className="mt-5">{t('reactivate_subscription.message')}</h5>
        <button
          className="button primary text-base font-semibold mb-5 rounded-lg mt-5 text-center"
          onClick={() => router.push('/user/subscription')}>
          {t('reactivate_subscription.button')}
        </button>
      </div>
    );
  }

  return (
    <div className="flex justify-center my-[12vh]">
      <div style={{ flexWrap: 'wrap', maxWidth: 430 }}>
        <div
          className="flex flex-wrap justify-center mb-28 md:mb-28"
          style={{ padding: '0 5.69444%', position: 'relative' }}>
          <svg width="132" height="132" viewBox="0 0 132 132" className="circular-progress">
            <circle className="bg"></circle>
            <circle className="fg"></circle>
          </svg>
          <div className="counter-container">
            <span className="counter"></span>
          </div>
        </div>
        <div className="text-center" style={{}}>
          <h3 className="" style={{ maxWidth: '70%', margin: '0 auto 20px auto' }}>
            {t('title')}
          </h3>
          <p className="" style={{ maxWidth: '90%', margin: '0 auto 36px auto' }}>
            {t('description')}
          </p>
          <ul className="flex flex-wrap justify-center" style={{}}>
            {tags.map((tag, index) => (
              <QuestionCategoryTag key={tag} {...{ tag, index }} />
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default memo(Calculation);
