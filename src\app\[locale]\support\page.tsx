'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/navigation';

type Question = {
  slug: String;
  question: JSX.Element;
};

type Section = {
  category: string;
  questions: Question[];
};

export default function SupportFAQ() {
  const router = useRouter();
  const t = useTranslations('support_page.sections');

  const faqData: Section[] = [
    {
      category: t('subscription.title'),
      questions: [
        {
          question: <>{t('subscription.questions.automatic_renewals')}</>,
          slug: 'automatic-renewals',
        },
        {
          question: <>{t('subscription.questions.update_email')}</>,
          slug: 'update-email',
        },
        {
          question: <>{t('subscription.questions.cancel_subscription')}</>,
          slug: 'cancel-subscription',
        },
        {
          question: <>{t('subscription.questions.lose_access')}</>,
          slug: 'lose-access-to-results',
        },
      ],
    },
    {
      category: t('billing.title'),
      questions: [
        {
          question: <>{t('billing.questions.subscription_charge')}</>,
          slug: 'subscription-charge',
        },
        {
          question: <>{t('billing.questions.request_refund')}</>,
          slug: 'request-refund',
        },
        {
          question: <>{t('billing.questions.unexpected_charge')}</>,
          slug: 'expect-charge',
        },
      ],
    },
    {
      category: t('policy.title'),
      questions: [
        {
          question: <a href="https://blog.iqinternational.org/privacy-policy">{t('policy.links.privacy')}</a>,
          slug: '',
        },
        {
          question: <a href="https://blog.iqinternational.org/terms-and-conditions">{t('policy.links.terms')}</a>,
          slug: '',
        },
        {
          question: (
            <a href="https://blog.iqinternational.org/terms-and-conditions/#:~:text=your%20own%20risk.-,6.%20Refund%20Policy,-6.1%20Trial%20Refunds">
              {t('policy.links.refund')}
            </a>
          ),
          slug: '',
        },
        {
          question: <a href="https://blog.iqinternational.org/cookie-policy">{t('policy.links.cookie')}</a>,
          slug: '',
        },
      ],
    },
    {
      category: t('help.title'),
      questions: [
        {
          question: <>{t('help.contact_us')}</>,
          slug: 'contact-us',
        },
      ],
    },
  ];

  const handleQuestionClick = (question: JSX.Element, slug: String) => {
    if (question.type === React.Fragment) {
      router.push(`/support/${slug}`);
    }
  };

  return (
    <div className="w-full max-w-[1120px] mx-auto flex flex-col md:mt-[30px]">
      <div className="w-full max-w-[1120px] mt-[8.5px] md:mt-[34px] grid grid-cols-1 md:grid-cols-2 gap-x-[192px] gap-y-[0] md:gap-y-[32px] font-ppmori mb-[24.5px] md:mb-0">
        {faqData.map((section, sectionIndex) => (
          <div
            key={sectionIndex}
            className="flex flex-col gap-[16px] px-[33px] md:px-[17px] py-[15.5px] md:py-[17.5px]">
            <h2
              className="text-[20px] text-[#0D0D0E] font-semibold leading-[27px] border-b tracking-[0]"
              style={{ borderBottomWidth: '3px', borderColor: '#0D0D0E' }}>
              {section.category}
            </h2>
            <div className="flex flex-col gap-[12px]">
              {section.questions.map((item, idx) => (
                <button
                  key={idx}
                  onClick={() => handleQuestionClick(item.question, item.slug)}
                  className="w-full text-left flex justify-between items-center font-normal text-[15px] leading-[24px] tracking-[0] text-[#454F69] hover:text-[#FF932F]">
                  {item.question}
                  <span className="text-[#FF932F]">{'>'}</span>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
