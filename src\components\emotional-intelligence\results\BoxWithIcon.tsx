'use client';
import Image from 'next/image';
import React, { ReactNode, useState } from 'react';

type BoxWithIcon = {
  source: string;
  alt: string;
  heading: string;
  children?: ReactNode;
  open: boolean;
};

const BoxWithIcon: React.FC<BoxWithIcon> = ({ source, alt, heading, children, open }) => {
  const [isOpen, setIsOpen] = useState<boolean>(open);

  const toggleContent = () => setIsOpen(!isOpen);

  return (
    <div className="flex flex-col md:flex-row gap-0 md:gap-[56px] box-content p-4 md:p-7 bg-white border-l-4 border-[#8C36D0] shadow-[0px_4px_14px_0px_#5400691A] rounded-[12px] z-20">
      <div className="flex md:flex-col gap-[16px] md:gap-[12px]">
        <Image src={source} alt={alt} width={70} height={70} className="md:hidden w-[44px] h-auto md:w-[70px]" />
        <label className="block font-ppmori font-semibold text-[#0C0113] text-[18px] md:text-[26px] leading-[22px] my-auto md:leading-[30px]">
          {heading}
        </label>
        <label className="hidden md:block font-ppmori font-normal text-[#8C8492] text-[14px] md:text-[16px] leading-[20px] md:leading-[24px]">
          {children}
        </label>
        <Image
          src={
            isOpen
              ? `/images/emotional-intelligence/landing/hide.svg`
              : `/images/emotional-intelligence/landing/show.svg`
          }
          alt="show/hide button"
          width={20}
          height={20}
          className="md:hidden w-[20px] h-[20px] my-auto justify-between ml-auto"
          onClick={toggleContent}
        />
      </div>
      <div className="md:hidden">
        {isOpen && (
          <label className="block font-ppmori font-normal text-[#8C8492] text-[14px] md:text-[16px] leading-[20px] mt-4 md:leading-[24px]">
            {children}
          </label>
        )}
      </div>
      <Image
        src={source}
        alt={alt}
        width={70}
        height={70}
        className="hidden mb-auto md:block w-[44px] h-auto md:w-[70px]"
      />
    </div>
  );
};

export default BoxWithIcon;
