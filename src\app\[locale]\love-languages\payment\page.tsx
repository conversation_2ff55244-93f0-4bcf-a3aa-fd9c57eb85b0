'use client';
import React, { useContext, ChangeEvent, useState, useEffect, useCallback } from 'react';
import { useRouter } from '@/lib/i18n/navigation';
import Logo from '@/components/love-languages/Logo';
import IconWithOrder from '@/components/love-languages/IconWithOrder';
import IconWithSmallSupport from '@/components/love-languages/IconWithSmallSupport';
import PaymentCards from '@/components/love-languages/PaymentCards';
import LanguageCheckoutForm from '@/components/love-languages/Payment/LanguageCheckoutForm';
import SessionContext from '@/store/SessionContext';
import UiContext from '@/store/UiContext';
import { addFormDataToSessionDb } from '@/services/session';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import PaymentDetails from '@/components/PaymentDetails';
import { debounce } from 'lodash';

const createPaymentIntent = httpsCallable(functions, 'createPaymentIntent');
const emailCheck = httpsCallable(functions, 'checkIfEmailExists');
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const Payment: React.FC = () => {
  const router = useRouter(); // Initialize useRouter

  const [clientSecret, setClientSecret] = useState('');
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const { formData, updateFormData, fetchAllSessionData, sessionId, prices, results } = useContext(SessionContext);
  const { time } = useContext(UiContext);

  let clickId: string | null = typeof window != 'undefined' ? window.clickflare?.data?.event_tokens?.click_id : null;
  let landingUrl = typeof localStorage != 'undefined' ? localStorage?.getItem('landingUrl') : null;
  if (!clickId && landingUrl && landingUrl != 'null') {
    try {
      const parsedLandingUrl = new URL(landingUrl);
      if (!clickId) clickId = parsedLandingUrl.searchParams.get('click_id');
    } catch (e) {
      console.error('Error parsing landingUrl:', e);
    }
  }

  const stripeOptions: StripeElementsOptions = {
    clientSecret,
    locale: 'en',
    appearance: {
      theme: 'stripe',
      rules: {
        '.Label': {
          color: '#0E2432',
          fontFamily: 'Raleway, sans-serif',
          fontSize: '14px',
          fontWeight: '600',
          marginBottom: '8px',
          paddingTop: '0',
        },
        '.Input': {
          border: '1px solid #ebebeb',
          borderRadius: '10px',
          paddingTop: '14px',
          paddingBottom: '14px',
          color: '#0E2432',
        },
        select: {
          display: 'none',
        },
      },
    },
  };

  useEffect(() => {
    if (results.length === 0) {
      router.push('/love-languages/test');
    }
    const urlClientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    //addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'love-languages' });
    const initializePaymentIntent = async () => {
      if ((email && isEmailValid && emailError === '') || urlClientSecret) {
        try {
          // Directly asserting the type on API call response
          const emailCheckResponse = (await emailCheck({
            email: localStorage.getItem('loveLanguageEmail') || email,
          })) as { data: { exists: boolean } };

          if (!emailCheckResponse.data.exists || urlClientSecret) {
            const res = (await createPaymentIntent({
              email:
                typeof window !== 'undefined' ? localStorage.getItem('loveLanguageEmail') || email : formData.email,
              name: formData.name,
              sessionId,
              clickId,
              landingUrl: localStorage.getItem('landingUrl'),
              country: prices.country,
              completionTime: time,
              checkoutVersion: time < 330 ? 'compliant' : 'v1',
              price: prices.oneTime.amount,
              receipt_email: email,
              type: 'LL-page',
              metadata: {
                email,
              },
            })) as { data: { clientSecret: string } };

            setClientSecret(res.data.clientSecret);
          } else {
            setIsEmailValid(false);
            setEmailError('This email is already in use. Please use a different email.');
          }
        } catch (error) {
          console.error('Error initializing payment intent:', error);
        }
      }
    };

    initializePaymentIntent();
  }, [email, isEmailValid, formData, time]);

  const reCreateClientSecret = () => {
    const urlClientSecret = new URLSearchParams(window.location.search).get('payment_intent_client_secret');
    const useremail = typeof window !== 'undefined' ? localStorage.getItem('loveLanguageEmail') || email : email;
    updateFormData({ ...formData, ['email']: useremail });
    addFormDataToSessionDb({ ...fetchAllSessionData(), time, type: 'love-languages' });
    if ((email && isEmailValid && emailError === '') || urlClientSecret) {
      createPaymentIntent({
        email: typeof window !== 'undefined' ? localStorage.getItem('loveLanguageEmail') : formData.email,
        name: 'formData.name',
        sessionId,
        clickId,
        landingUrl: localStorage.getItem('landingUrl'),
        country: prices.country,
        completionTime: time,
        checkoutVersion: time < 330 ? 'compliant' : 'v1',
        price: prices.oneTime.amount,
        receipt_email: email,
        metadata: {
          email,
        },
      }).then((res: any) => {
        setClientSecret(res.data.clientSecret);
      });
    }
  };

  const handleEmailChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setEmail(value);
    updateFormData({ ...formData, ['email']: value });
    setEmailError(validateEmail(value));
    debouncedValidateEmail(value);

    console.log('updateemail', formData);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) ? '' : 'Please enter a valid email address';
  };

  // Debounced function to validate email
  const debouncedValidateEmail = useCallback(
    debounce((value: string) => {
      const isValid = validateEmail(value);
      setEmailError(isValid);

      if (isValid.length === 0) {
        console.log('Valid email:', value);
        setIsEmailValid(true);
        if (typeof window !== 'undefined') {
          localStorage.setItem('loveLanguageEmail', value);
        }
        reCreateClientSecret();
      } else {
        setIsEmailValid(false);
      }
    }, 500), // Adjust debounce delay (500ms)
    []
  );

  return (
    <div className="max-w-[1440px] mx-auto">
      <header className="w-full flex justify-center items-center py-[13px] md:py-[43px] mb-[27px] md:mb-[18px]">
        <Logo />
      </header>
      <main>
        <div
          id="paymentDetails"
          className="px-[16px] md:px-[81px] flex-col flex lg:flex-row md:gap-[44px] md:mb-[40px]">
          <div className="w-full md:pl-[80px] order-2 md:order-none">
            <div className="flex flex-col items-center mb-[16px] md:mb-[24px]">
              <div className="w-full flex flex-row justify-between mb-5 md:mb-6">
                <span className="font-raleway font-bold text-[20px] md:text-[26px] leading-[30px] md:leading-[32px] text-[#0E2432] tracking-normal md:tracking-tight">
                  Order Details
                </span>
                <span
                  className="flex items-center gap-[10px] py-[6px] md:py-[8px] px-[10px] md:px-[12px] bg-[#F6F6F6] rounded-[8px] text-[14px] md:text-[16px] leading-[18px] md:leading-[20px] font-raleway font-medium text-[#0E2432]"
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                  7-Day Full Access
                </span>
              </div>
              <div className="w-full grid grid-cols-1 gap-2 md:gap-3 mb-3 md:mb-4">
                <IconWithOrder source="/images/love-languages/order-1.png" alt="order-1">
                  Your personalized <br />
                  Love Languages Report
                </IconWithOrder>
                <IconWithOrder source="/images/love-languages/order-2.png" alt="order-2">
                  Personal Growth Challenge
                </IconWithOrder>
                <IconWithOrder source="/images/love-languages/order-3.png" alt="order-3">
                  Plan to Fortify Your Connections
                </IconWithOrder>
                <IconWithOrder source="/images/love-languages/order-4.png" alt="order-4">
                  Access from any device anywhere
                </IconWithOrder>
              </div>
              <div className="w-full bg-[#E1F4FF] rounded-[12px] p-3 md:py-5 md:px-6 flex flex-row justify-between mb-5 md:mb-6">
                <label className="font-raleway font-bold text-[#0E2432] text-[16px] leading-[24px] md:text-[20px] md:leading-[26px]">
                  Total
                </label>
                <label
                  className="font-raleway font-bold text-[#0E2432] text-[16px] leading-[24px] md:text-[20px] md:leading-[26px]"
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
                  {prices.oneTime.formatted}
                </label>
              </div>
              <PaymentCards mb_desktop="20px" mb_mobile="24px" h_mobile="28px" h_desktop="40px" />
              <div className="w-full opacity-50 border border-[#E2E2E2] mb-4 md:mb-5"></div>
              <div className="flex gap-[8px] justify-between md:gap-[6px] w-full flex-col md:flex-row">
                <IconWithSmallSupport source="/images/love-languages/check-dark.png" alt="check-dark">
                  14 days refund
                </IconWithSmallSupport>
                <IconWithSmallSupport source="/images/love-languages/check-dark.png" alt="check-dark">
                  Premium Support
                </IconWithSmallSupport>
                <IconWithSmallSupport source="/images/love-languages/check-dark.png" alt="check-dark">
                  Satisfaction guarantee
                </IconWithSmallSupport>
              </div>
            </div>
          </div>
          <div className="w-full flex flex-col items-center order-1 md:order-none mb-[32px] md:mb-0">
            <div className="w-full flex flex-col gap-4 md:gap-6 p-4 md:p-6 shadow-[0px_4px_12px_rgba(0,_34,_45,_0.08)] rounded-[12px] bg-white mb-6">
              <label className="font-raleway font-bold text-[20px] md:text-[26px] leading-[24px] md:leading-[34px] text-[#0E2432]">
                What&apos;s Your Email address?
              </label>
              <div className="w-full flex flex-col gap-2">
                <label className="font-raleway font-semibold text-[#0E2432] text-[14px] leading-[20px]">
                  Email address
                </label>
                <input
                  className="bg-white font-raleway font-medium text-[16px] px-4 py-[14px] leading-[20px] text-[#7A8C97] border border-[#EBEBEB] rounded-[10px]"
                  placeholder="Enter your email"
                  value={email}
                  onChange={handleEmailChange}
                  style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}
                />
                {emailError && <span className="text-red-500 font-raleway text-sm">{emailError}</span>}
              </div>
            </div>
            <div className="relative w-full flex flex-col shadow-[0px_4px_12px_rgba(0,_34,_45,_0.08)] rounded-[12px] bg-white font-raleway">
              {!isEmailValid && (
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    cursor: 'not-allowed',
                    backgroundColor: '#ccc',
                    opacity: 0.5,
                    borderRadius: '10px',
                    zIndex: '999',
                    // Could add a subtle background or tooltip here
                    // backgroundColor: 'rgba(255, 255, 255, 0)',
                  }}
                  onClick={() => {
                    // If you want a custom message or tooltip:
                    console.log('You must enter an email before using Apple/Google Pay');
                  }}
                />
              )}
              {clientSecret && (
                <div className="flex flex-wrap p-4 md:p-6">
                  {clientSecret && (
                    <Elements options={stripeOptions} stripe={stripePromise}>
                      <LanguageCheckoutForm
                        clientSecret={clientSecret}
                        email={email}
                        // reCreateClientSecret={reCreateClientSecret}
                      />
                    </Elements>
                  )}
                </div>
              )}
              {!clientSecret && (
                <div className="opacity-10">
                  <img className="w-full hidden sm:flex" src="/images/love-land-card-placeholder.svg" />
                  <img className="w-full sm:hidden" src="/images/love-land-card-placeholder-mobile.svg" />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Payment;
