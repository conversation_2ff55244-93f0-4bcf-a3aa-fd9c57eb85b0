import React from 'react';
import { personalgrowthchallenge } from '@/app/[locale]/love-languages/results/PersonalGrowthChallenge';

type PersonalGrowthChallengeSecondaryFocusProps = {
  category: string;
};

const PersonalGrowthChallengeSecondaryFocus: React.FC<PersonalGrowthChallengeSecondaryFocusProps> = ({ category }) => {
  return (
    <div className="flex flex-col gap-[24px] md:gap-[28px] md:pr-[58px]">
      {personalgrowthchallenge[category]?.map((secondary, index) => (
        <div className="flex flex-col gap-2" key={index + 1}>
          <h4
            className="font-raleway font-bold text-[#0E2432] text-[20px] leading-[26px] md:text-[24px] md:leading-[32px]"
            style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
            {index + 1}. {secondary.title}
          </h4>
          <p className="font-raleway font-medium text-[14px] leading-[20px] md:text-[16px] md:leading-[24px] text-[#828E98]">
            {secondary.description}
          </p>
        </div>
      ))}
    </div>
  );
};

export default PersonalGrowthChallengeSecondaryFocus;
