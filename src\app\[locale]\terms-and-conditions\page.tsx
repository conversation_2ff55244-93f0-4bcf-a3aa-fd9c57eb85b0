'use client';

import { snakeCase } from 'lodash';
import { Link } from '@/lib/i18n/navigation';
import { useContext, useMemo } from 'react';
import SessionContext from '@/store/SessionContext';

const TermsAndConditions = () => {
  const { siteConfig } = useContext(SessionContext);
  const data = useMemo(
    () => [
      {
        title: 'Accounts and membership',
        text: [
          `You must be at least 18 years of age to use the Website and Services. By using the Website and Services and by agreeing to this Agreement you warrant and represent that you are at least 18 years of age. If you create an account on the Website, you are responsible for maintaining the security of your account and you are fully responsible for all activities that occur under the account and any other actions taken in connection with it. We may, but have no obligation to, monitor and review new accounts before you may sign in and start using the Services. Providing false contact information of any kind may result in the termination of your account. You must immediately notify us of any unauthorized uses of your account or any other breaches of security. We will not be liable for any acts or omissions by you, including any damages of any kind incurred as a result of such acts or omissions. We may suspend, disable, or delete your account (or any part thereof) if we determine that you have violated any provision of this Agreement or that your conduct or content would tend to damage our reputation and goodwill. If we delete your account for the foregoing reasons, you may not re-register for our Services. We may block your email address and Internet protocol address to prevent further registration.`,
        ],
      },
      {
        title: 'Billing and payments',
        text: [
          'You shall pay all fees or charges to your account in accordance with the fees, charges, and billing terms in effect at the time a fee or charge is due and payable. Where Services are offered on a trial basis, payment may be required after the trial period ends, and not when you enter your billing details (which may be required prior to the commencement of the trial period). If auto-renewal is enabled for the Services you have subscribed for, you will be charged automatically in accordance with the term you selected. Sensitive and private data exchange happens over a SSL secured communication channel and is encrypted and protected with digital signatures, and the Website and Services are also in compliance with PCI vulnerability standards in order to create as secure of an environment as possible for Users. Scans for malware are performed on a regular basis for additional security and protection. If, in our judgment, your purchase constitutes a high-risk transaction, we will require you to provide us with a copy of your valid government-issued photo identification, and possibly a copy of a recent bank statement for the credit or debit card used for the purchase. We reserve the right to refuse any order you place with us. We may, in our sole discretion, limit or cancel quantities purchased per person, per household or per order. These restrictions may include orders placed by or under the same customer account, the same credit card, and/or orders that use the same billing and/or shipping address. In the event that we make a change to or cancel an order, we may attempt to notify you by contacting the e-mail and/or billing address/phone number provided at the time the order was made.',
        ],
      },
      {
        title: 'Pricing and Payment terms',
        text: [
          '3.1. Pricing Structure',
          [
            'Trial Period: New users can access a trial period for 0.70 EUR, granting full access to our services for a limited duration. This trial fee is refundable upon request for 30 days after starting the trial. If the trial is not canceled before it ends, the subscription automatically converts to a monthly plan at 39.90 EUR.',
            `Monthly Subscription: After the trial period, users are enrolled in a monthly subscription at 39.90 EUR per month, providing ongoing access to all features and services offered by ${siteConfig.siteName}.`,
            'Additional Services: Optional additional services or products may be offered, with clear pricing provided. Users must opt-in to incur any additional charges.',
          ],
          '3.2. Payment Terms',
          [
            'Billing Cycle: Subscription fees are billed in advance on a recurring monthly basis unless otherwise stated. The payment method provided during sign-up will be automatically charged at the beginning of each billing cycle.',
            `Automatic Renewal: By subscribing, you authorize ${siteConfig.siteName} to charge your selected payment method for the recurring subscription fee without additional notice unless you cancel your subscription.`,
            `Payment Methods: ${siteConfig.siteName} accepts major credit cards, debit cards, and other electronic payment methods listed on the website. Users are responsible for maintaining up-to-date and valid payment information.`,
            'Payment Failure: If a payment is declined, you will be notified to update your payment details. Failure to resolve payment issues within the specified timeframe may result in suspension or termination of your account.',
            'Taxes: All prices include applicable taxes unless otherwise specified. Users are responsible for any additional taxes or duties in their jurisdiction.',
          ],
          '3.3. Price Adjustments',
          [
            `Right to Modify Prices: ${siteConfig.siteName} reserves the right to change pricing at any time. Price changes will be communicated to users at least 30 days in advance and will apply to subsequent billing cycles.`,
            'Notification of Changes: Users will be notified of any price changes via email or website notification. If you do not agree to the new prices, you must cancel your subscription before the new rates take effect.',
          ],
          '3.4. Cancellation and Refund Policy',
          [
            'Cancellations: You may cancel your subscription at any time through your account settings or by contacting customer support. Cancellations take effect at the end of the current billing cycle, and you will not be charged for the subsequent cycle. Access to services continues until the end of the current paid period.',
            'Refunds: The trial fee of 0.70 EUR is refundable upon request if made within 30 days after the start of the trial period. Once the trial ends and the subscription begins, the subscription fee is non-refundable. This policy applies to all subscription fees after the commencement of the paid subscription.',
            `Pro-Rata Refunds: ${siteConfig.siteName} does not provide pro-rata refunds for partial months or unused services. Once a billing cycle starts, the full subscription fee for that period is due.`,
          ],
          '3.5. Disputed Charges',
          [
            `Dispute Resolution: Any disputes regarding billing must be reported within 30 days of the charge in question. ${siteConfig.siteName} is committed to resolving disputes promptly and fairly. If you believe there has been an error, please contact our customer support team immediately for resolution. Failure to report a dispute within this period may result in the waiver of your right to contest the charge.`,
            `Chargebacks: Initiating a chargeback with your bank or payment provider without first contacting ${siteConfig.siteName} to resolve the issue is considered a violation of these Terms. Such actions may lead to immediate termination of your account and possible legal action to recover owed amounts. Please be advised that we reserve the right to contest any chargebacks and provide evidence of service delivery to the issuing bank. Resolving disputes directly with us is typically faster and more efficient, ensuring continued access to our services.`,
          ],
          `3.6. Account Termination Due to Non-Payment`,
          `Termination: ${siteConfig.siteName} reserves the right to terminate your account in the event of non-payment. Any outstanding balances will remain due, and ${siteConfig.siteName} may pursue collection efforts, including legal action.`,
        ],
      },
      {
        title: 'Accuracy of information',
        text: [
          'Occasionally there may be information on the Website that contains typographical errors, inaccuracies or omissions that may relate to product descriptions, pricing, promotions and offers. We reserve the right to correct any errors, inaccuracies or omissions, and to change or update information or cancel orders if any information on the Website or Services is inaccurate at any time without prior notice (including after you have submitted your order). We undertake no obligation to update, amend or clarify information on the Website including, without limitation, pricing information, except as required by law. No specified update or refresh date applied on the Website should be taken to indicate that all information on the Website or Services has been modified or updated.',
        ],
      },
      {
        title: 'Prohibited uses',
        text: [
          'In addition to other terms as set forth in the Agreement, you are prohibited from using the Website and Services or Content: (a) for any unlawful purpose; (b) to solicit others to perform or participate in any unlawful acts; (c) to violate any international, federal, provincial or state regulations, rules, laws, or local ordinances; (d) to infringe upon or violate our intellectual property rights or the intellectual property rights of others; (e) to harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate based on gender, sexual orientation, religion, ethnicity, race, age, national origin, or disability; (f) to submit false or misleading information; (g) to upload or transmit viruses or any other type of malicious code that will or may be used in any way that will affect the functionality or operation of the Website and Services, third party products and services, or the Internet; (h) to spam, phish, pharm, pretext, spider, crawl, or scrape; (i) for any obscene or immoral purpose; or (j) to interfere with or circumvent the security features of the Website and Services, third party products and services, or the Internet. We reserve the right to terminate your use of the Website and Services for violating any of the prohibited uses.',
        ],
      },

      {
        title: 'Limitation of liability',
        text: [
          `To the fullest extent permitted by applicable law, in no event will ${siteConfig.siteName}, its affiliates, directors, officers, employees, agents, suppliers or licensors be liable to any person for any indirect, incidental, special, punitive, cover or consequential damages (including, without limitation, damages for lost profits, revenue, sales, goodwill, use of content, impact on business, business interruption, loss of anticipated savings, loss of business opportunity) however caused, under any theory of liability, including, without limitation, contract, tort, warranty, breach of statutory duty, negligence or otherwise, even if the liable party has been advised as to the possibility of such damages or could have foreseen such damages. To the maximum extent permitted by applicable law, the aggregate liability of ${siteConfig.siteName} and its affiliates, officers, employees, agents, suppliers and licensors relating to the services will be limited to an amount no greater than one euro or any amounts actually paid in cash by you to ${siteConfig.siteName} for the prior one month period prior to the first event or occurrence giving rise to such liability. The limitations and exclusions also apply if this remedy does not fully compensate you for any losses or fails of its essential purpose.`,
        ],
      },
      {
        title: 'Changes and amendments',
        text: [
          'We reserve the right to modify this Agreement or its terms related to the Website and Services at any time at our discretion. When we do, we will revise the updated date at the bottom of this page. We may also provide notice to you in other ways at our discretion, such as through the contact information you have provided.',
          'An updated version of this Agreement will be effective immediately upon the posting of the revised Agreement unless otherwise specified. Your continued use of the Website and Services after the effective date of the revised Agreement (or such other act specified at that time) will constitute your consent to those changes.',
        ],
      },
      {
        title: 'Acceptance of these terms',
        text: [
          'You acknowledge that you have read this Agreement and agree to all its terms and conditions. By accessing and using the Website and Services you agree to be bound by this Agreement. If you do not agree to abide by the terms of this Agreement, you are not authorized to access or use the Website and Services.',
        ],
      },
      {
        title: 'Contacting us ',
        text: [
          'If you have any questions, concerns, or complaints regarding this Agreement, we encourage you to contact us 24/7 using the details below:',
          `Contact us at ${siteConfig.supportEmail}`,
        ],
      },
    ],
    []
  );

  return (
    <div>
      <div className="w-full h-[100px] sm:h-[130px]"></div>
      <section id="privacy" className={`flex flex-wrap justify-between max-w-[1440px] m-auto mt-5 pt-0`} style={{}}>
        <div className="flex flex-wrap flex-col max-w-[416px] w-full lg:w-[40%]">
          <div style={{}}>
            <h1 className="mb-10 text-4xl" style={{ maxWidth: 353 }}>
              Terms And Conditions
            </h1>
          </div>
          <ol style={{ color: '#191919', listStyleType: 'decimal', paddingLeft: 20 }}>
            {data.map((item, i) => (
              <a key={i} href={`#${snakeCase(item.title)}`}>
                <li style={{ paddingLeft: 6, marginBottom: 12 }}>{`${item.title}`}</li>
              </a>
            ))}
          </ol>
        </div>
        <div className="max-w-[736px]  w-full lg:w-[60%]" style={{}}>
          <div>
            <h3 className="pb-[40px]">Introduction</h3>
            <p className="pb-[40px]">
              These terms and conditions (“Agreement”) set forth the general terms and conditions of your use of the
              iqeurope.org website (“Website” or “Service”) and any of its related products and services (collectively,
              “Services”). This Agreement is legally binding between you (“User”, “you” or “your”) and {siteConfig.siteName} (“{siteConfig.siteName}”
              , “we”, “us” or “our”). If you are entering into this agreement on behalf of a business or other
              legal entity, you represent that you have the authority to bind such entity to this agreement, in which
              case the terms “User”, “you” or “your” shall refer to such entity. If you do not have such authority, or
              if you do not agree with the terms of this agreement, you must not accept this agreement and may not
              access and use the Website and Services. By accessing and using the Website and Services, you acknowledge
              that you have read, understood, and agree to be bound by the terms of this Agreement. You acknowledge that
              this Agreement is a contract between you and {siteConfig.siteName}, even though it is electronic and is not physically
              signed by you, and it governs your use of the Website and Services.
            </p>
          </div>
          {data.map((item, index) => (
            <div key={index}>
              <h3
                className="big"
                id={snakeCase(item.title)}
                style={{ marginBottom: 24, marginTop: index > 0 ? 48 : 0, scrollMarginTop: 150 }}>
                {item.title}
              </h3>
              {item.text.map((paragraph, i) =>
                paragraph.constructor === Array ? (
                  <div key={i}>
                    <ul style={{ listStyleType: 'disc', paddingLeft: 40 }}>
                      {paragraph.map((li, i) => (
                        <li key={i}>{li}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <p key={i} style={{ marginBottom: 16, marginTop: index > 0 ? 16 : 0, whiteSpace: 'pre-line' }}>
                    {paragraph}
                    {item.title.includes('Cookies') && i === 1 && (
                      <span>
                        <Link className="hover:underline text-my-orange mb-4" target="_blank" href="/cookies">
                          Cookie Policy
                        </Link>
                        {'.'}
                      </span>
                    )}
                  </p>
                )
              )}
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default TermsAndConditions;