'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from 'recharts';
import React, { useState, useEffect } from 'react';

interface GaugeChartProps {
  id: string;
  chart_color_from: string;
  chart_color_to: string;
  chart_drop_shadow: string;
  remaining_color: string;
  bar_bg: string;
  bar_border: string;
  value: number;
  maxValue: number;
  value_color: string;
  maxValue_color: string;
}

export function GaugeChart({
  id,
  chart_color_from,
  chart_color_to,
  chart_drop_shadow,
  remaining_color,
  bar_bg,
  bar_border,
  value,
  maxValue,
  value_color,
  maxValue_color,
}: GaugeChartProps) {
  const [displayValue, setDisplayValue] = useState<number | string>(' '); // Initialize with the same value as the server

  useEffect(() => {
    // Update the value on the client side after hydration
    setDisplayValue(value);
  }, [value]);

  const data = [
    { name: 'Score', value: typeof displayValue === 'number' ? displayValue : 0 },
    { name: 'Remaining', value: typeof displayValue === 'number' ? maxValue - displayValue : maxValue },
  ];

  // Calculate position of the indicator bar dynamically
  const progressPercentage = typeof displayValue === 'number' ? displayValue / maxValue : 0;
  const endAngle = 180 - progressPercentage * 180; // Maps the progress to the 180-degree arc

  // Position of the small indicator bar
  const barPositionX = 50 + 65 * Math.cos((endAngle * Math.PI) / 180);
  const barPositionY = 67 - 65 * Math.sin((endAngle * Math.PI) / 180);
  const barPositionX2 = 50 + 41 * Math.cos((endAngle * Math.PI) / 180);
  const barPositionY2 = 67 - 41 * Math.sin((endAngle * Math.PI) / 180);

  return (
    <div className="relative w-[223px] md:w-[554px] h-[112px] md:h-[206px]">
      <ResponsiveContainer width="100%" height="160%">
        <PieChart>
          <defs>
            {/* Define a gradient */}
            <linearGradient id={`bg_${id}`} x1="30%" y1="100%" x2="100%" y2="20%">
              <stop offset="0%" stopColor={chart_color_from} />
              <stop offset="91.56%" stopColor={chart_color_to} />
            </linearGradient>

            {/* Drop shadow filter */}
            <filter id={`dropshadow_${id}`} x="-20%" y="-20%" width="150%" height="150%">
              <feDropShadow dx="0" dy="0" stdDeviation="8" floodColor={chart_drop_shadow} />
            </filter>
          </defs>
          <Pie
            data={data}
            cy="68%"
            startAngle={180}
            endAngle={0}
            innerRadius="90%"
            outerRadius="130%"
            paddingAngle={0}
            dataKey="value"
            cornerRadius={5}
            isAnimationActive={false}>
            {data.map((entry, index) => {
              const isFirstCell = index === 0;

              return (
                <Cell
                  key={`cell-${index}`}
                  fill={isFirstCell ? `url(#bg_${id})` : remaining_color} // Apply gradient or fallback color
                  strokeWidth={0}
                  style={isFirstCell ? { filter: `url(#dropshadow_${id})` } : undefined}
                />
              );
            })}
          </Pie>
          {/* Small Bar/Line at the end */}
          <svg viewBox="0 0 100 100" width="100%" height="100%" className="absolute top-0 left-0 pointer-events-none">
            <line
              x1={`${barPositionX}%`}
              y1={`${barPositionY}%`}
              x2={`${barPositionX2}%`}
              y2={`${barPositionY2}%`}
              stroke={bar_border} // Purple border
              strokeWidth="1.5" // Thicker border line
              strokeLinecap="round"
            />
            <line
              x1={`${barPositionX}%`}
              y1={`${barPositionY}%`}
              x2={`${barPositionX2}%`}
              y2={`${barPositionY2}%`}
              stroke={bar_bg} // White fill
              strokeWidth="1" // Thinner inner line
              strokeLinecap="round"
            />
          </svg>
        </PieChart>
      </ResponsiveContainer>
      <div className="absolute inset-0 top-[90px] md:top-[185px] text-center font-ppmori font-semibold tracking-[-0.03em]">
        <span
          className={`text-[28px] md:text-[48px] leading-[28px] md:leading-[32px] bg-gradient-to-r ${value_color} text-transparent bg-clip-text`}>
          {displayValue}{' '}
        </span>
        <span className="md:text-[36px] md:leading-[32px]" style={{ color: `${maxValue_color}` }}>
          of {maxValue}
        </span>
      </div>
    </div>
  );
}
