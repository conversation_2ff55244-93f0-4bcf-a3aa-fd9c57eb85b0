import { getRequestConfig } from 'next-intl/server';
import { headers } from 'next/headers';
import { Locale, defaultLocale, locales } from './locales';

/* ------------------------------------------------------------------ *
 * Helpers                                                            *
 * ------------------------------------------------------------------ */

/** Narrow a value to the `Locale` enum. */
function isLocale(value: unknown): value is Locale {
  return typeof value === 'string' && (locales as string[]).includes(value);
}

/** Lazy-load the correct JSON file for the given locale. */
async function loadMessages(locale: Locale) {
  return (await import(`../../locales/${locale}.json`)).default;
}

/* ------------------------------------------------------------------ *
 * Request configuration                                              *
 * ------------------------------------------------------------------ */

export default getRequestConfig(async ({ requestLocale }) => {
  // ── 1. Resolve the requested locale ──────────────────────────────
  // First try to get locale from middleware headers
  const headersList = headers();
  const localeFromHeaders = headersList.get('x-locale');
  const requested = await requestLocale;

  let locale = defaultLocale;

  // Priority: middleware detected locale > requested locale > default
  if (localeFromHeaders && isLocale(localeFromHeaders)) {
    locale = localeFromHeaders as Locale;
  } else if (requested && isLocale(requested)) {
    locale = requested as Locale;
  }

  // ── 2. Return Next-Intl config object ────────────────────────────
  return {
    locale,
    messages: await loadMessages(locale)
  };
});