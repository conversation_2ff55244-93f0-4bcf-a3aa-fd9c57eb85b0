'use client';

import Image from 'next/image';
import { useContext } from 'react';
import { Link } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';

const FooterLogo = () => {
  const { siteConfig } = useContext(SessionContext);

  return (
    <Link href="/" className="h-fit">
      <Image
        className="w-[216.81px] h-[26.21px] md:w-[300.2px] md:h-[33.52px]"
        src={siteConfig.logo.path}
        alt="Logo"
        width={siteConfig.logo.width}
        height={siteConfig.logo.height}
        quality={100}
        priority
      />
    </Link>
  );
};

export default FooterLogo;
