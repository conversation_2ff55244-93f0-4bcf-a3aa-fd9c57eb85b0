# Public Host URL
NEXT_PUBLIC_HOST_URL=http://localhost:3000
NEXT_PUBLIC_VERCEL_URL=http://localhost:3000

# Firebase Configuration
NEXT_PUBLIC_FB_API_KEY=
NEXT_PUBLIC_FB_AUTH_DOMAIN=
NEXT_PUBLIC_FB_PROJECT_ID=
NEXT_PUBLIC_FB_STORAGE_BUCKET=
NEXT_PUBLIC_FB_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FB_APP_ID=

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=

# Files Bucket
NEXT_PUBLIC_FILES_BUCKET=

# Functions Emulator Configuration
NEXT_PUBLIC_CONNECT_FUNCTIONS_EMULATOR=true
NEXT_PUBLIC_FUNCTIONS_EMULATOR_HOST=127.0.0.1
NEXT_PUBLIC_FUNCTIONS_EMULATOR_PORT=5001

# Use Payment (solidgate | stripe)
NEXT_PUBLIC_USE_PAYMENT=solidgate

# Multi-language Support
# Set to 'true' to enable multiple languages (stage/dev)
# Set to 'false' or omit for production (English only)
NEXT_PUBLIC_ENABLE_MULTI_LANGUAGE=false

# Default Locale Configuration
# The default language to use when no other language is detected
NEXT_PUBLIC_DEFAULT_LOCALE=en

# Locale Prefix Strategy
# 'never' = No language codes in URLs (e.g., /pricing)
# 'as-needed' = Language codes only for non-default locales (e.g., /pricing, /de/pricing)
# 'always' = Language codes for all locales (e.g., /en/pricing, /de/pricing)
NEXT_PUBLIC_LOCALE_PREFIX=never