import { Prices } from '@/app/prices';
import { InitConfig } from '@solidgate/react-sdk';
import { getSiteConfig } from '../../site.config';

export type Answer = {
  questionId: number;
  answerId: number;
};

export type FormData = { email: string; name: string };

export interface SessionData {
  answers: Answer[];
  checkoutId: string;
  formData: FormData;
  locale: string;
  paymentStatus: string;
  questionId: number;
  sessionAddedToSendGrid?: boolean;
  sessionId: string;
  time?: number;
  stage?: number; // Added for test progress
  clickedButton?: string | null; // Added for selected answer
  testAnswers?: string[]; // Added for test answers
  emotionalStage?: number; // Added for emotional intelligence test progress
  emotionalClickedButton?: string | null; // Added for selected answer in emotional intelligence test
  emotionalTestAnswers?: number[]; // Added for emotional intelligence test answers
}

export interface Score {
  category: string;
  percentage: string;
  sum: number;
}

export interface SessionContextProps {
  sessionId: string;
  locale: string;
  questionId: number;
  answers: Answer[];
  formData: FormData;
  checkoutId: string;
  paymentStatus: string;
  getIsTrial: () => boolean;
  prices: Prices;
  siteConfig: ReturnType<typeof getSiteConfig>;
  results: Score[];
  stripeInvoices: { data: any[] };
  solidgateInvoices: { data: any[] };
  emotionalScores: Record<string, any>; // Specify the type as needed
  stage: number; // Added for test progress
  clickedButton: string | null; // Added for selected answer
  testAnswers: string[]; // Added for test answers
  emotionalStage: number; // Added for emotional intelligence test progress
  emotionalClickedButton: string | null; // Added for selected answer in emotional intelligence test
  emotionalTestAnswers: number[]; // Added for emotional intelligence test answers
  paymentSystem: 'stripe' | 'solidgate' | null;
  merchantData: InitConfig['merchantData'] | null;
  updateSessionId: (newClickId: string) => Promise<void>;
  updateLocale: (newLocale: string) => Promise<void>;
  updateQuestionId: (newQuestionId: number) => Promise<void>;
  updateAnswer: ({ questionId, answerId }: { questionId: number; answerId: number }) => Promise<void>;
  updateAllAnswers: (answers: Answer[]) => Promise<void>;
  updateStripeInvoices: (invoices: { data: any[] }) => Promise<void>;
  updateSolidgateInvoices: (invoices: { data: any[] }) => Promise<void>;
  updateFormData: (newFormData: FormData) => Promise<void>;
  updateCheckoutId: (newCheckoutId: string) => Promise<void>;
  updatePaymentStatus: (newPaymentStatus: string) => Promise<void>;
  fetchAllSessionData: () => SessionData;
  updateAllSessionData: (data: SessionData) => Promise<void>;
  resetAnswers: () => Promise<void>;
  updatePaymentSystem: (system: string) => void;
  updateResults: (newResults: any) => Promise<void>;
  updateEmotionalScores: (newEmotionalScores: any) => Promise<void>;
  updateStage: (newStage: number) => Promise<void>; // Added for test progress
  updateClickedButton: (newClickedButton: string | null) => Promise<void>; // Added for selected answer
  updateTestAnswers: (newTestAnswers: string[]) => Promise<void>; // Added for test answers
  resetTestState: () => Promise<void>; // Added to reset test-related state
  updateEmotionalStage: (newStage: number) => Promise<void>; // Added for emotional intelligence test progress
  updateEmotionalClickedButton: (newClickedButton: string | null) => Promise<void>; // Added for selected answer in emotional intelligence test
  updateEmotionalTestAnswers: (newTestAnswers: number[]) => Promise<void>; // Added for emotional intelligence test answers
  updateMerchantData: (newMerchantData: InitConfig['merchantData']) => Promise<void>;
  resetEmotionalTestState: () => Promise<void>; // Added to reset emotional intelligence test state,
}

export interface UiContextProps {
  completedByNumber: number;
  updateCompletedByNumber: (newCompletedByNumber: number) => Promise<void>;
  today: string;
  time: number;
  updateToday: (newToday: string) => Promise<void>;
  updateTime: (newTime: number) => Promise<void>;
}

export interface UserContextProps {
  uid: string;
  email: string;
  setUser: ({ uid, email }: { uid: string; email: string }) => Promise<void>;
}

export interface UserData {
  uid: string;
  name: string;
  email: string;
  active: boolean;
  sessionId: string;
}

export interface TrainingSessionContextProps {
  trainingSessionId: string;
  questionIdByCategory: { analytical: number; pattern: number; visual: number };
  answersByCategory: { analytical: Answer[]; pattern: Answer[]; visual: Answer[] };
  updateTrainingSessionId: (newTrainingSessionId: string) => Promise<void>;
  updateQuestionIdByCategory: ({
    newQuestionId,
    category,
  }: {
    newQuestionId: number;
    category: string;
  }) => Promise<void>;
  updateAnswerByCategory: ({
    questionId,
    answerId,
    category,
  }: {
    questionId: number;
    answerId: number;
    category: string;
  }) => Promise<void>;
}

export enum PostHogEventEnum {
  START_TEST = 'Start test',
  QUESTION_VIEWED = 'Question viewed',
  QUESTION_ANSWERED = 'Question answered',
  QUESTION_SKIPPED = 'Question skipped',
  BACK_TO_PREVIOUS_QUESTION = 'Back to previous question',
  GET_RESULTS_CLICKED = 'Get results clicked',
  CONFIRMED_GET_RESULTS = 'Confirmed get results',
  DECLINED_GET_RESULTS = 'Declined get results',
  CALCULATION_PAGE_VIEWED = 'Calculation page viewed',
  ACCOUNT_CREATION_PAGE_VIEWED = 'Account creation page viewed',
  ACCOUNT_FORM_SUBMITTED = 'Account form submitted',
  ACCOUNT_FORM_SUBMITTED_ERROR = 'Account form submitted error',
  ACCOUNT_FORM_SUBMITTED_SUCCESS = 'Account form submitted success',
  CHECKOUT_PAGE_VIEWED = 'Checkout page viewed',
  CHECKOUT_CONFIRMED = 'Checkout confirmed',
  PAYMENT_SUCCESS = 'Payment success',
  DOWNLOAD_REPORT_CLICKED = 'Download report clicked',
  DOWNLOAD_CERTIFICATE_CLICKED = 'Download certificate clicked',
  GO_TO_MEMBERS_AREA = 'Go to members area',
  GO_TO_FAQ_PAGE = 'Go to FAQ page',
}
