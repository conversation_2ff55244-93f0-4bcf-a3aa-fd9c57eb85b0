'use client';

import React, { useContext } from 'react';
import dayjs from 'dayjs';
import Image from 'next/image';
import { Link } from '@/lib/i18n/navigation';
import SessionContext from '@/store/SessionContext';
import { centsToCurrency } from '../../utils';

const InvoicesStripe = () => {
  const { stripeInvoices, solidgateInvoices } = useContext(SessionContext);

  return (
    <div className="flex flex-col items-center w-full mt-5 2xl:mt-10 px-[5%] md:px-[5.69444%]">
      <div className="mt-[150px]">
        {stripeInvoices?.data?.map(x => {
          const date = dayjs(x.effective_at * 1000).format('DD.MM.YYYY');
          return (
            <div key={x.id} className="flex gap-3 mt-5">
              <div>
                Amount: <span className="font-bold text-primary">{centsToCurrency(x.total)}</span>{' '}
                <span>{x.currency.toUpperCase()},</span>
              </div>
              <div>
                Status: <span className="font-bold text-primary">{x.paid ? 'Paid' : 'Not-paid'}</span>
              </div>
              <div>
                Date: <span className="font-bold text-primary">{date}</span>
              </div>
              <Link href={x.hosted_invoice_url} className="cursor-pointer">
                <Image
                  src={'/images/external-link-outline.svg'}
                  alt={'external link'}
                  width={18}
                  height={18}
                  className="w-[16px] h-[16px]"
                />
              </Link>
            </div>
          );
        })}
        {solidgateInvoices.data.map(x => {
          const date = dayjs(x.billing_period_started_at).format('DD.MM.YYYY');
          return (
            <div key={x.id} className="flex gap-3 mt-5">
              <div>
                Amount: <span className="font-bold text-primary">{centsToCurrency(x.amount)}</span> <span>USD,</span>
              </div>
              <div>
                Status: <span className="font-bold text-primary">{x.status}</span>
              </div>
              <div>
                Date: <span className="font-bold text-primary">{date}</span>
              </div>
              {/* <Link href={x.hosted_invoice_url} className="cursor-pointer">
                  <Image
                    src={'/images/external-link-outline.svg'}
                    alt={'external link'}
                    width={18}
                    height={18}
                    className="w-[16px] h-[16px]"
                  />
                </Link> */}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default InvoicesStripe;
