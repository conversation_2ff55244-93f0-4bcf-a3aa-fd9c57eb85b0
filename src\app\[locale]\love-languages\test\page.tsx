'use client';
import React, { useState, useEffect, useContext } from 'react';
import { useRouter } from '@/lib/i18n/navigation';
import ProgressBar from '@/components/love-languages/ProgressBar';
import Logo from '@/components/love-languages/Logo';
import Button<PERSON>ithAnswer from '@/components/love-languages/ButtonWithAnswer';
import { questions } from './questions';
import { categories } from './categories';
import SessionContext from '@/store/SessionContext';
import { UserContext } from '@/store/UserContext';

const Test: React.FC = () => {
  const { user } = useContext(UserContext);
  const router = useRouter();
  const {
    stage: contextStage,
    clickedButton: contextClickedButton,
    testAnswers: contextTestAnswers,
    updateStage,
    updateClickedButton,
    updateTestAnswers,
    updateResults,
  } = useContext(SessionContext); // Use the context to store session state and results

  const totalStages = questions.length;
  const [stage, setStage] = useState(contextStage || 1);
  const [clickedButton, setClickedButton] = useState<string | null>(contextClickedButton || null);
  const [answers, setAnswers] = useState<string[]>(contextTestAnswers || new Array(totalStages).fill(''));

  const progress = (stage / totalStages) * 100;

  const handleNext = (answer: string) => {
    setClickedButton(answer);
    const updatedAnswers = [...answers];
    updatedAnswers[stage - 1] = answer;
    setAnswers(updatedAnswers);

    if (stage < totalStages) {
      setStage(stage + 1);
    }
  };

  const handleBack = () => {
    if (stage > 1) {
      const previousAnswer = answers[stage - 2];
      setClickedButton(previousAnswer || null);
      setStage(stage - 1);
    }
  };

  const currentStage = questions[stage - 1];
  const isLastAnswerSelected = answers[totalStages - 1] !== '';

  // Update local state when context state changes
  useEffect(() => {
    setStage(contextStage || 1);
    setClickedButton(contextClickedButton || null);
    setAnswers(contextTestAnswers || new Array(totalStages).fill(''));
  }, [contextStage, contextClickedButton, contextTestAnswers, totalStages]);

  // Update context state whenever stage, clickedButton, or answers change
  useEffect(() => {
    updateStage(stage);
    updateClickedButton(clickedButton);
    updateTestAnswers(answers);
  }, [stage, clickedButton, answers, updateStage, updateClickedButton, updateTestAnswers]);

  // Calculate sum counts and percentages
  const calculateResults = () => {
    const totalPoints = 49; // Total maximum points
    const categoryScores: { [key: string]: number } = {};

    // Initialize scores for each category
    Object.keys(categories).forEach(category => {
      categoryScores[category] = 0;
    });

    // Add points based on answers
    answers.forEach(answer => {
      Object.entries(categories).forEach(([category, options]) => {
        if (options.includes(answer)) {
          categoryScores[category]++;
        }
      });
    });

    // Calculate sum counts and percentages
    const results = Object.entries(categoryScores).map(([category, score]) => ({
      category,
      sum: score,
      percentage: ((score / totalPoints) * 100).toFixed(0),
    }));

    // Sort results by sum in descending order
    results.sort((a, b) => b.sum - a.sum);
    return results;
  };

  const handleSubmit = () => {
    const results = calculateResults();
    updateResults(results); // Save results to the context
    user ? router.push('/love-languages/results') : router.push('/love-languages/checkout'); // Redirect to the results page
  };

  return (
    <div className="m-[auto]">
      <header className="w-full h-[60px] md:h-[110px] flex justify-center items-center pt-[4px] md:pt-[12px]">
        <Logo />
      </header>
      <ProgressBar progress={progress} />

      <main className="max-w-[343px] md:max-w-[619px] mx-auto mt-[32px] md:mt-[60px]">
        <span className="font-raleway text-[20px] md:text-2xl font-bold leading-8">{currentStage.question}</span>
        <div className="mt-[20px] md:mt-8 flex flex-col gap-[12px] md:gap-4">
          {currentStage.answers.map((answer, index) => (
            <ButtonWithAnswer
              key={index}
              label={answer}
              onClick={() => handleNext(answer)}
              isClicked={clickedButton === answer}
            />
          ))}
        </div>

        <div className="mt-[24px] md:mt-[40px] flex justify-between">
          {stage > 1 && (
            <button
              className="flex justify-center items-center px-10 py-5 w-[140px] md:w-[150px] h-[48px] md:h-[58px] border border-[#5DC4FF] rounded-[10px] font-raleway font-bold text-[16px] md:text-xl tracking-[-0.03em] text-[#5DC4FF]"
              onClick={handleBack}>
              Back
            </button>
          )}
          {stage === totalStages && isLastAnswerSelected && (
            <button
              className="flex justify-center items-center px-10 py-5 w-[140px] md:w-[150px] h-[48px] md:h-[58px] bg-[#5DC4FF] rounded-[10px] font-raleway font-bold md:text-xl tracking-[-0.03em] text-[16px] text-[#FDFEFF]"
              onClick={handleSubmit}>
              Submit
            </button>
          )}
        </div>
      </main>
    </div>
  );
};

export default Test;
