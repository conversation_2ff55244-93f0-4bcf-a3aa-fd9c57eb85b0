'use client';

import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import CheckoutForm from '@/components/CheckoutForm';
import { useContext } from 'react';
import SessionContext from '@/store/SessionContext';

// Make sure to call loadStripe outside of a component’s render to avoid
// recreating the Stripe object on every render.
// This is your test publishable API key.
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function Payment({ clientSecret }: { clientSecret: string }) {
  const { prices, getIsTrial } = useContext(SessionContext);

  return (
    <div id="paymentDetails" className="flex justify-center">
      {clientSecret && (
        <div className="flex flex-col">
          <h4 className="text-center md:text-left mb-4 md:mb-6">Payment Details</h4>
          <div className="mx-5">
            <Elements options={{ clientSecret, appearance: { theme: 'stripe' } }} stripe={stripePromise}>
              <CheckoutForm />
            </Elements>
            {getIsTrial() ? (
              <p className="text-xs mt-4">
                Our IQ test service is exceptionally offered at the price of {prices.oneTime.formatted}
                {prices.vatIncluded ? ' (incl. VAT)' : ''} for a 2-day trial. At the end of the trial period and without
                cancellation from your side, our offer will be automatically renewed as a non-binding subscription, at
                the price of {prices.subscription.formatted} per month{prices.vatIncluded ? ' (incl. VAT)' : ''}.
              </p>
            ) : (
              <p className="text-xs mt-4">
                Our IQ test service is offered at the price of {prices.subscription.formatted}
                {prices.vatIncluded ? ' (incl. VAT)' : ''} per month as a non-binding subscription.
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
