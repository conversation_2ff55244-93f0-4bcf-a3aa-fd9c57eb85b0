'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import CountryCard from '@/components/Cards/HomePage/CountryCard';
import countries from './countries';

const CountriesSection = () => {
  const t = useTranslations('home_page');
  const [isFullList, setIsFullList] = useState<boolean>(false);

  return (
    <section className='text-center' style={{}}>
      <h2 className='big max-w-[94%] md:max-w-[496px] m-auto' style={{}}>
        {t('header_avg_scores')}
      </h2>
      <p className='max-w-[94%] md:max-w-[496px] mt-6 mb-16 mx-auto' style={{}}>
        {t('body_avg_scores')}
      </p>
      <ol className='flex flex-wrap justify-center gap-5'>
        {countries.map((country, i) => (
          <CountryCard key={i} {...{ country, i, isFullList }} />
        ))}
      </ol>
      <button
        className='inline-flex items-center button secondary justify-center w-[90%] max-w-[412px] md:max-w-52 font-semibold mx-3 sm:mx-0 mt-10'
        style={{
          fontSize: 22,
          borderRadius: 10,
          lineHeight: '150%',
        }}
        onClick={() => setIsFullList(!isFullList)}
      >
        {isFullList ? t('btn_country_list_collapse') : t('btn_country_list')}
      </button>
    </section>
  );
};

export default CountriesSection;
