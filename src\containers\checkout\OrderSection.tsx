import { useContext } from 'react';
import { useTranslations } from 'next-intl';
import PaymentTagSection from '@/containers/checkout/PaymentTagSection';
import ProductCard from '@/components/Cards/CheckoutPage/ProductCard';
import SessionContext from '@/store/SessionContext';

interface OrderSectionProps {
  compliantVersion: boolean;
}

const OrderSection = ({ compliantVersion }: OrderSectionProps) => {
  const t = useTranslations('checkout.order_section');
  const { prices, getIsTrial } = useContext(SessionContext);
  
  const products = [
    {
      title: t('products.iq_score.title'),
      text: t('products.iq_score.text'),
      img: '/checkout/score.svg',
    },
    {
      title: t('products.certificate.title'),
      text: t('products.certificate.text'),
      img: '/checkout/certificate.png',
    },
    {
      title: t('products.report.title'),
      text: t('products.report.text'),
      img: '/checkout/report.png',
    },
    ...(compliantVersion && getIsTrial()
      ? [
          {
            title: `Trial Price Only ${prices.oneTime.formatted}`,
            text: `Enjoy full access for 2 days at ${prices.oneTime.formatted}${
              prices.vatIncluded ? ' (incl. VAT)' : ''
            }, then continue with ${prices.subscription.formatted}/month${
              prices.vatIncluded ? ' (incl. VAT)' : ''
            }. Cancel anytime, no commitment.`,
            img: '/checkout/percent.png',
          },
        ]
      : []),
  ];

  return (
    <div className="">
      <h4 className="text-center md:text-left mb-4 md:mb-6">{t('title')}</h4>
      <ul className="flex flex-wrap">
        {products.map((product, index) => (
          <ProductCard key={index} {...{ product, index: index + 1, showOrdinals: !compliantVersion }} />
        ))}
      </ul>
      <div className="hidden md:block w-full">
        <PaymentTagSection />
        <hr />
      </div>
    </div>
  );
};

export default OrderSection;
